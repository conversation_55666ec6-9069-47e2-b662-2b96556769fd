# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
ignore_*
.cursor/
# Virtual Environment
venv/
env/
ENV/
penv/
.env
.venv
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.project
.pydevproject
.settings/

# Testing
.coverage
htmlcov/
.tox/
.nox/
.pytest_cache/
coverage.xml
*.cover
.hypothesis/

# Logs
*.log
logs/
log/

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.db
*.sqlite3

# FastAPI specific
.pytest_cache/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

