import pytest
import asyncio
from unittest.mock import AsyncMock
from uuid import uuid4
from app.core.services.request_log_service import RequestLogService
from app.core.models.request_log import RequestType, RequestStatus, RequestLog
from app.infra.db.repositories.project_repository_impl import ProjectRepositoryImpl
from app.infra.db.repositories.request_log_repository_impl import RequestLogRepositoryImpl
from app.core.models.project import Project
from datetime import datetime, UTC

from app.infra.mongo_client.chemstack_db_client import chemstack_db_client
from app.infra.literature_service.ai_patent_processor_impl import AIPatentProcessorImpl
from app.infra.literature_service.es.patent_search_service import PatentSearchService

@pytest.mark.asyncio
async def test_tag_update_integration_with_real_count():
    await chemstack_db_client.connect()

    project_repo = ProjectRepositoryImpl()
    request_log_repo = RequestLogRepositoryImpl()
    patent_search_service = PatentSearchService()
    ai_patent_processor = AIPatentProcessorImpl(patent_search_service)

    tenant_id = uuid4()
    project_id = uuid4() 
    user_id = uuid4()
    now = datetime.now(UTC)
    project = Project(
        id=project_id,
        tenant_id=tenant_id,
        compound_name="TestCompound",
        canonical_smiles="C1=CC=CC=C1",
        status="in_progress",
        molecule_image_url="",
        created_by=user_id,
        updated_by=user_id,
        created_at=now,
        updated_at=now,
        tags=["Well Studied", "Literature Rich", "Emerging Research", "Under-Researched"]
    )
    await project_repo.create_project(tenant_id, project)

    request_id = "8b048ebd-b091-4a6c-b43c-b23f74c397c3"  # replace with request_id present in azure
    await request_log_repo.create_log(RequestLog(
        id=request_id,
        tenant_id=tenant_id,
        project_id=project_id,
        user_id=user_id,
        request_type=RequestType.PATENT,
        status=RequestStatus.SUCCESS,
        created_at=now,
        updated_at=now,
    ))

    # Fetch the real count from Azure
    count = await ai_patent_processor.get_patent_count_by_request_ids([request_id])
    print(f"Azure returned count: {count}")

    # Service under test
    service = RequestLogService(
        request_log_repository=request_log_repo,
        project_repository=project_repo,
        ai_patent_processor=ai_patent_processor
    )

    payload = {"request_id": str(request_id), "status": "SUCCESS"}
    await service.process_request_queue_status(RequestType.PATENT, payload)

    updated_project = await project_repo.get_project(tenant_id, project_id)
    tag_options = ["Well Studied", "Literature Rich", "Emerging Research", "Under-Researched"]
    tags = [t for t in (updated_project.tags or []) if t in tag_options]

    # Now you can assert based on the real count
    if count > 50:
        assert tags == ["Well Studied"]
    elif 11 <= count <= 50:
        assert tags == ["Literature Rich"]
    elif 6 <= count <= 10:
        assert tags == ["Emerging Research"]
    elif 0 < count <= 5:
        assert tags == ["Under-Researched"]