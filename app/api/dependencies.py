from typing import Annotated

from fastapi import Depends

# Services
from app.core.outbound.repositories.research_overview_repository import ResearchOverviewRepository
from app.core.services.project_service import ProjectService
from app.core.services.retro_route_service import RetroRouteService
from app.core.services.route_service import RouteService
from app.core.services.step_service import StepService
from app.core.services.reaction_service import ReactionService
from app.core.services.project_note_service import ProjectNoteService
from app.core.services.request_log_service import RequestLogService
from app.core.services.patent_service import PatentService
from app.core.services.research_overview_service import ResearchOverviewService

# Repositories (Interfaces)
from app.core.outbound.repositories.project_repository import ProjectRepository
from app.core.outbound.repositories.reaction_repository import ReactionRepository
from app.core.outbound.repositories.request_log_repository import RequestLogRepository
from app.core.outbound.repositories.route_repository import RouteRepository
from app.core.outbound.repositories.step_repository import StepRepository
from app.core.outbound.repositories.project_note_repository import ProjectNoteRepository


# Object Store Interface
from app.core.outbound.storage.object_store import ObjectStore

# Repository Implementations
from app.infra.db.repositories.project_repository_impl import ProjectRepositoryImpl
from app.infra.db.repositories.reaction_repository_impl import ReactionRepositoryImpl
from app.infra.db.repositories.request_log_repository_impl import RequestLogRepositoryImpl
from app.infra.db.repositories.research_overview_repository_impl import ResearchOverviewRepositoryImpl
from app.infra.db.repositories.route_repository_impl import RouteRepositoryImpl
from app.infra.db.repositories.step_repository_impl import StepRepositoryImpl
from app.infra.db.repositories.project_note_repository_impl import ProjectNoteRepositoryImpl

from app.infra.storage.azure_object_store_impl import AzureObjectStore
from app.core.outbound.retrorunner.retro_runner_service import RetroRunnerService
from app.infra.retrorunner.retro_runner_service_impl import RetroRunnerServiceImpl
from app.core.outbound.repositories.retro_route_review_repository import RetroRouteReviewRepository
from app.infra.db.repositories.retro_route_review_repository_impl import RetroRouteReviewRepositoryImpl
from app.infra.retrorunner.db.retro_route_repository_impl import RetroRouteRepositoryImpl
from app.infra.literature_service.ai_patent_processor_impl import AIPatentProcessorImpl
from app.infra.literature_service.es.patent_search_service import PatentSearchService
from app.core.outbound.literature_service.ai_patent_processor import AIPatentProcessor


# Project Service
async def get_project_service() -> ProjectService:
    project_repository = await get_project_repository()
    request_log_repository = await get_request_log_repository()
    ai_patent_processor = await get_ai_patent_processor()
    retro_runner_service = await get_retro_runner_service()
    object_store = await get_blob_uploader_repository()
    project_note_repository = await get_project_note_repository()
    return ProjectService(project_repository, request_log_repository, ai_patent_processor, retro_runner_service, object_store, project_note_repository)


# AI Patent Processor
async def get_ai_patent_processor() -> AIPatentProcessor:
    patent_search_service = await get_patent_search_service()
    return AIPatentProcessorImpl(patent_search_service)


# Patent Search Service
async def get_patent_search_service() -> PatentSearchService:
    return PatentSearchService()


# Retro Route Service
async def get_retro_route_service() -> RetroRouteService:
    retro_runner_service = await get_retro_runner_service()
    request_log_repository = await get_request_log_repository()
    retro_route_review_repository = await get_retro_route_review_repository()
    project_repository = await get_project_repository()
    return RetroRouteService(retro_runner_service, request_log_repository, retro_route_review_repository, project_repository)


# Retro Runner Service
async def get_retro_runner_service() -> RetroRunnerService:
    return RetroRunnerServiceImpl(RetroRouteRepositoryImpl())


# Retro Route Review Repository
async def get_retro_route_review_repository() -> RetroRouteReviewRepository:
    return RetroRouteReviewRepositoryImpl()


# Route Service
async def get_route_service() -> RouteService:
    route_repository = await get_route_repository()
    return RouteService(route_repository)


# Step Service
async def get_step_service() -> StepService:
    step_repository = await get_step_repository()
    return StepService(step_repository)


# Reaction Service
async def get_reaction_service() -> ReactionService:
    reaction_repository = await get_reaction_repository()
    return ReactionService(reaction_repository)


# Request Log Service
async def get_request_log_service() -> RequestLogService:
    request_log_repository = await get_request_log_repository()
    project_repository = await get_project_repository()
    return RequestLogService(request_log_repository, project_repository)


# Research Workspace Service
async def get_project_note_service() -> ProjectNoteService:
    project_note_repository = await get_project_note_repository()
    return ProjectNoteService(project_note_repository)


# Patent Service
async def get_patent_service() -> PatentService:
    request_log_repository = await get_request_log_repository()
    ai_patent_processor = await get_ai_patent_processor()
    return PatentService(request_log_repository, ai_patent_processor)

#Research Overview Service
async def get_research_overview_service() -> ResearchOverviewService:
    reseach_overview_repository = await get_research_overview_repository()
    return ResearchOverviewService(reseach_overview_repository)

async def get_project_repository() -> ProjectRepository:
    return ProjectRepositoryImpl()


async def get_route_repository() -> RouteRepository:
    return RouteRepositoryImpl()


async def get_step_repository() -> StepRepository:
    return StepRepositoryImpl()


async def get_reaction_repository() -> ReactionRepository:
    return ReactionRepositoryImpl()


async def get_project_note_repository() -> ProjectNoteRepository:
    return ProjectNoteRepositoryImpl()


async def get_request_log_repository() -> RequestLogRepository:
    return RequestLogRepositoryImpl()


async def get_blob_uploader_repository() -> ObjectStore:
    return AzureObjectStore()

async def get_research_overview_repository() -> ResearchOverviewRepository:
    return ResearchOverviewRepositoryImpl()


# Type aliases for dependency injection
ProjectServiceDep = Annotated[ProjectService, Depends(get_project_service)]
RouteServiceDep = Annotated[RouteService, Depends(get_route_service)]
StepServiceDep = Annotated[StepService, Depends(get_step_service)]
ReactionServiceDep = Annotated[ReactionService, Depends(get_reaction_service)]
ProjectNoteServiceDep = Annotated[ProjectNoteService, Depends(get_project_note_service)]
RequestLogServiceDep = Annotated[RequestLogService, Depends(get_request_log_service)]
PatentServiceDep = Annotated[PatentService, Depends(get_patent_service)]
RetroRouteServiceDep = Annotated[RetroRouteService, Depends(get_retro_route_service)]
ResearchOverviewServiceDep = Annotated[ResearchOverviewService, Depends(get_research_overview_service)]
