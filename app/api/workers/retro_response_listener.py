import asyncio

from loguru import logger

from app.core.services.request_log_service import RequestLogService
from app.infra.db.repositories.request_log_repository_impl import RequestLogRepositoryImpl
from app.infra.db.repositories.project_repository_impl import ProjectRepositoryImpl
from app.infra.literature_service.ai_patent_processor_impl import AIPatentProcessorImpl
from app.infra.literature_service.es.patent_search_service import PatentSearchService
from app.core.models.request_log import RequestType
from app.worker import celery_consumer_app


# Output queue task listener
@celery_consumer_app.task(name="retro_result_handler", bind=True, autoretry_for=(Exception,),
                          retry_kwargs={'max_retries': 5, 'countdown': 10}, retry_backoff=True)
def update_retro_request(self, payload: dict):
    try:
        logger.info(f"Retro request received: {payload}")
        service = RequestLogService(RequestLogRepositoryImpl(), ProjectRepositoryImpl(), AIPatentProcessorImpl(PatentSearchService()))
        return asyncio.get_event_loop().run_until_complete(service.process_request_queue_status(RequestType.RETRO, payload))
    except Exception as e:
        logger.error(f"Error in handle_retro_output task: {e}")
        raise
