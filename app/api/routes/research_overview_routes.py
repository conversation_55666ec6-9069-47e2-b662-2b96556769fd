from fastapi import APIRouter, Depends
from app.api.schemas.research_overview import ResearchOverviewResponse, ResearchOverviewData
from app.api.dependencies import ResearchOverviewServiceDep
from app.common.request_context import get_request_context

router = APIRouter(prefix="/research-overview", tags=["Research Overview"])

@router.get("", response_model=ResearchOverviewResponse)
async def research_overview(research_overview_service: ResearchOverviewServiceDep):
    tenant_id, user_id = get_request_context()
    research_overview = await research_overview_service.get_overview(tenant_id)
    # Convert domain model to schema for response
    response_data = ResearchOverviewData.model_validate(research_overview.model_dump())
    return ResearchOverviewResponse(
        success=True,
        data=response_data,
        message="Research overview fetched successfully"
    ) 
