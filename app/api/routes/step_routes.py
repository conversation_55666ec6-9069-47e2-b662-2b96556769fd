from fastapi import APIRouter, HTTPException

from app.core.models.step import Step as CoreStep
from app.api.dependencies import StepServiceDep
from app.api.schemas.step import (
    Step,
    StepCreate,
    StepUpdate,
    StepResponse,
    StepListResponse,
)
from app.common.request_context import get_request_context

router = APIRouter(prefix="/projects/{project_id}/routes/{route_id}/steps", tags=["Experimentation: Step Management"])


@router.get("", response_model=StepListResponse)
async def get_steps(
    project_id: str,
    route_id: str,
    step_service: StepServiceDep,
    skip: int = 0,
    limit: int = 100,
):
    """Get all steps for a route"""
    tenant_id, user_id = get_request_context()
    core_steps = await step_service.get_steps_by_route(tenant_id, project_id, route_id, skip, limit)
    # Convert core models to schema models
    steps = [Step.model_validate(step.model_dump()) for step in core_steps]
    total = len(steps)  # In a real app, you'd get total from the service
    return StepListResponse(data=steps, total=total, skip=skip, limit=limit, message="Steps retrieved successfully")


@router.post("", response_model=StepResponse)
async def create_step(
    project_id: str,
    route_id: str,
    step: StepCreate,
    step_service: StepServiceDep,
):
    """Create a new step"""
    tenant_id, user_id = get_request_context()
    step_data = step.model_dump()
    step_data["project_id"] = project_id
    step_data["route_id"] = route_id
    step_data["tenant_id"] = tenant_id
    step_data["created_by"] = user_id
    step_data["updated_by"] = user_id
    # Convert schema model to core model
    core_step = CoreStep.model_validate(step_data)
    step = await step_service.create_step(tenant_id, project_id, route_id, core_step)
    return StepResponse(data=step, message="Step created successfully")


@router.get("/{step_id}", response_model=StepResponse)
async def get_step(
    project_id: str,
    route_id: str,
    step_id: str,
    step_service: StepServiceDep,
):
    """Get a step by ID"""
    tenant_id, user_id = get_request_context()
    core_step = await step_service.get_step_by_id(tenant_id, project_id, route_id, step_id)
    if not core_step:
        raise HTTPException(status_code=404, detail="Step not found")
    # Convert core model to schema model
    step = Step.model_validate(core_step.model_dump())
    return StepResponse(data=step, message="Step retrieved successfully")


@router.put("/{step_id}", response_model=StepResponse)
async def update_step(
    project_id: str,
    route_id: str,
    step_id: str,
    step: StepUpdate,
    step_service: StepServiceDep,
):
    """Update a step"""
    tenant_id, user_id = get_request_context()
    # Convert schema model to core model
    core_step = CoreStep.model_validate(step.model_dump())
    core_step.route_id = route_id
    core_step.tenant_id = tenant_id
    core_step.updated_by = user_id
    success = await step_service.update_step(step_id, core_step)
    if not success:
        raise HTTPException(status_code=404, detail="Step not found")
    return StepResponse(data=success, message="Step updated successfully")
