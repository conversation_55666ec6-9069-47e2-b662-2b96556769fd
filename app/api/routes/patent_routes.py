from fastapi import APIRouter, Query
from typing import Optional, List
from uuid import UUID

from app.api.schemas.patent import (
    PatentListResponse,
    PatentResponse,
    PatentDetailResponse,
    PatentProcessingRequest,
    PatentFilterResponse,
    PatentSortBy,
    SortOrder
)
from app.common.request_context import get_request_context
from app.api.dependencies import PatentServiceDep
from app.api.schemas.common import SuccessResponse
from app.core.models.patent import PatentTaskData

router = APIRouter(prefix="/projects/{project_id}/patents", tags=["AI: Literature Content Management"])


@router.get("", response_model=PatentListResponse)
async def get_all_patents(
    project_id: UUID,
    patent_service: PatentServiceDep,
    sort_by: Optional[PatentSortBy] = Query(PatentSortBy.RELEVANCY_SCORE, description="Field to sort the list on, default = relevancy_score"),
    sort_order: Optional[SortOrder] = Query(SortOrder.DESCENDING, description="Order to sort the list in, default = desc"),
    reaction_types: Optional[List[str]] = Query(None, description="List of reaction types to filter by"),
    claim_types: List[Optional[str]] = Query(None, description="List of claim types to filter by "),
    filing_companies: List[Optional[str]] = Query(None, description="List of claim filing companies to filter by"),
    patent_offices: List[Optional[str]] = Query(None, description="List of patent issuing offices to filter by"),
    skip: int = Query(0, description="Number of items to skip"),
    limit: int = Query(100, description="Number of items to return"),
):
    tenant_id, user_id = get_request_context()

    paginated_patents, patent_generation_status = await patent_service.list_patents(
        tenant_id, project_id, sort_by, sort_order, reaction_types, claim_types, filing_companies, patent_offices, skip, limit
    )
    patent_responses = [PatentResponse.model_validate(patent.model_dump()) for patent in paginated_patents.data]
    return PatentListResponse(
        success=True,
        data=patent_responses,
        total=paginated_patents.total,
        skip=skip,
        limit=limit,
        generation_status=patent_generation_status,
        message="Patents retrieved successfully",
        trace_id="",
    )


@router.get("/filters", response_model=SuccessResponse[PatentFilterResponse])
async def get_patents_filter_data(
    project_id: UUID,
    patent_service: PatentServiceDep
):
    tenant_id, user_id = get_request_context()

    filter_data = await patent_service.get_patent_filters(tenant_id, project_id)
    filter_response = PatentFilterResponse.model_validate(filter_data.model_dump())

    return SuccessResponse(
        success=True, data=filter_response,
        message="Patent filters retrieved successfully",
        trace_id=""
    )


@router.get("/{patent_id}", response_model=SuccessResponse[PatentDetailResponse])
async def get_patent(
    patent_id: str,
    patent_service: PatentServiceDep
):
    # tenant_id, user_id = get_request_context()
    patent = await patent_service.get_patent(patent_id)
    patent_response = PatentDetailResponse.model_validate(patent.model_dump())
    return SuccessResponse(
        success=True, data=patent_response, message="Patent retrieved successfully", trace_id=""
    )


@router.post("/patent-task", response_model=SuccessResponse)
async def trigger_patent_task(
        project_id: UUID,
        patent_processing_request: PatentProcessingRequest,
        patent_service: PatentServiceDep,
):
    tenant_id, user_id = get_request_context()
    patent_task_data = PatentTaskData.model_validate(patent_processing_request.model_dump())
    await patent_service.trigger_patent_processing(
        tenant_id, project_id, user_id, patent_task_data
    )
    return SuccessResponse(success=True, message="Patent task triggered successfully", trace_id="", data=None)
