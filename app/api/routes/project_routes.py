from typing import Optional
from uuid import UUID

from fastapi import APIRouter, HTTPException, Query

from app.api.dependencies import ProjectServiceDep
from app.api.schemas.project import (
    Project,
    ProjectCreate,
    ProjectUpdate,
    ProjectResponse,
    ProjectListResponse,
    RemoveCollaboratorRequest,
    AddCollaboratorRequest,
)
from app.common.request_context import get_request_context, get_current_user_roles
from app.core.models.project import Project as CoreProject, Collaborator

router = APIRouter(prefix="/projects", tags=["Project Management"])

@router.get("", response_model=ProjectListResponse)
async def get_projects(
    project_service: ProjectServiceDep,
    status: Optional[str] = Query(None, description="Filter by project status"),
    name: Optional[str] = Query(None, description="Filter by project name"),
    skip: int = Query(0, description="Number of items to skip"),
    limit: int = Query(100, description="Number of items to return"),
):
    """Get all projects"""
    tenant_id, user_id = get_request_context()
    user_roles = get_current_user_roles()
    and_clause_filters = []
    if not user_roles or "CHEMSTACK_ADMIN" not in user_roles:
        auth_filters = {f"collaborators.{str(user_id)}.active": True}
        and_clause_filters.append(auth_filters)

    if status:
        and_clause_filters.append({"status": status})

    if name:
        or_name_filters = [
            {"name": {"$regex": name, "$options": "i"}},
            {"compound_name": {"$regex": name, "$options": "i"}},
        ]
        and_clause_filters.append({"$or": or_name_filters})

    if len(and_clause_filters) == 1:
        additional_filters = and_clause_filters[0]
    elif len(and_clause_filters) > 1:
        additional_filters = {"$and": and_clause_filters}
    else:
        additional_filters = {}

    paginated_projects = await project_service.get_projects_by_tenant(tenant_id, additional_filters, skip, limit)
    # Convert core models to schema models
    project_responses = [Project.model_validate(project.model_dump()) for project in paginated_projects.data]
    return ProjectListResponse(
        success=True, data=project_responses,
        total=paginated_projects.total, skip=skip, limit=limit,
        message="Projects retrieved successfully",
        trace_id=""
    )


@router.post("", response_model=ProjectResponse)
async def create_project(
    project: ProjectCreate,
    project_service: ProjectServiceDep,
):
    """Create a new project"""
    tenant_id, user_id = get_request_context()

    # Convert a schema model to dict and add tenant_id
    project_data = project.model_dump()
    project_data["tenant_id"] = tenant_id
    project_data["created_by"] = user_id
    project_data["updated_by"] = user_id

    # Convert to a core model
    core_project = CoreProject.model_validate(project_data)

    # Call the service and debug the response
    created_project = await project_service.create_project(tenant_id, core_project)

    # Convert a core model into a schema model
    project_response = Project.model_validate(created_project.model_dump())

    return ProjectResponse(success=True, data=project_response, message="Project created successfully")


@router.get("/search", response_model=ProjectListResponse)
async def search_projects(
    project_service: ProjectServiceDep,
    query: str = Query(..., description="Search query"),
    skip: int = Query(0, description="Number of items to skip"),
    limit: int = Query(100, description="Number of items to return")
):
    """Get searched projects"""
    tenant_id, user_id = get_request_context()
    paginated_projects = await project_service.search_projects(tenant_id, query, skip, limit)
    # Convert core models to schema models
    project_responses = [Project.model_validate(project.model_dump()) for project in paginated_projects.data]
    return ProjectListResponse(
        success=True, data=project_responses,
        total=paginated_projects.total, skip=skip, limit=limit,
        message="Projects retrieved successfully",
        trace_id=""
    )


@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: UUID,
    project_service: ProjectServiceDep,
):
    """Get a project by ID"""
    tenant_id, user_id = get_request_context()
    core_project = await project_service.get_project(tenant_id, project_id)
    if not core_project:
        raise HTTPException(status_code=404, detail="Project not found")
    # Convert a core model to a schema model
    project = Project.model_validate(core_project.model_dump())

    return ProjectResponse(success=True, data=project, message="Project retrieved successfully")


@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: UUID,
    project: ProjectUpdate,
    project_service: ProjectServiceDep,
):
    """Update a project"""
    tenant_id, user_id = get_request_context()
    # Convert schema model to core model
    project_data = project.model_dump()
    project_data["tenant_id"] = tenant_id
    project_data["updated_by"] = user_id
    project_data["id"] = project_id
    core_project = CoreProject.model_validate(project_data)
    updated_project = await project_service.update_project(tenant_id, project_id, core_project)

    # Convert the updated project to schema model
    project_response = Project.model_validate(updated_project.model_dump())

    return ProjectResponse(success=True, data=project_response, message="Project updated successfully")


@router.post("/{project_id}/add-collaborator", response_model=ProjectResponse)
async def add_collaborator_to_project(
    project_id: UUID,
    add_collaborator_request: AddCollaboratorRequest,
    project_service: ProjectServiceDep,
):
    """Add a user to a project"""
    tenant_id, user_id = get_request_context()
    collaborator = Collaborator.model_validate(add_collaborator_request.model_dump())
    updated_project = await project_service.add_collaborator_to_project(tenant_id, project_id, collaborator)

    # Convert the updated project to schema model
    project_response = Project.model_validate(updated_project.model_dump())
    return ProjectResponse(success=True, data=project_response, message="Project updated successfully")


@router.post("/{project_id}/remove-collaborator", response_model=ProjectResponse)
async def remove_user_from_project(
    project_id: UUID,
    remove_collaborator_request: RemoveCollaboratorRequest,
    project_service: ProjectServiceDep,
):
    """Remove a user from a project"""
    tenant_id, user_id = get_request_context()
    updated_project = await project_service.remove_collaborator_from_project(
        tenant_id, project_id, remove_collaborator_request.user_id
    )

    # Convert the updated project to schema model
    project_response = Project.model_validate(updated_project.model_dump())
    return ProjectResponse(success=True, data=project_response, message="Project updated successfully")
