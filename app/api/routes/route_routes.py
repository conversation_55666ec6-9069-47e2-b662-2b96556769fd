from typing import List
from fastapi import APIRouter, Depends, HTTPException
from uuid import UUID
from app.core.models.route import Route as CoreRoute
from app.api.dependencies import RouteServiceDep
from app.api.schemas.route import Route, RouteCreate, RouteUpdate, RouteResponse, RouteListResponse
from app.common.request_context import get_request_context

router = APIRouter(prefix="/projects/{project_id}/routes", tags=["Experimentation: Route Management"])


@router.get("", response_model=RouteListResponse)
async def get_routes(
    project_id: UUID,
    route_service: RouteServiceDep,
    skip: int = 0,
    limit: int = 100,
):
    """Get all routes for a project"""
    tenant_id, user_id = get_request_context()
    core_routes = await route_service.get_routes_by_project(tenant_id, project_id, skip, limit)
    # Convert core models to schema models
    routes = [Route.model_validate(route.model_dump()) for route in core_routes]
    total = len(routes)  # In a real app, you'd get total from the service
    return RouteListResponse(data=routes, total=total, skip=skip, limit=limit, message="Routes retrieved successfully")


@router.post("", response_model=RouteResponse)
async def create_route(
    project_id: str,
    route: RouteCreate,
    route_service: RouteServiceDep,
):
    """Create a new route"""
    tenant_id, user_id = get_request_context()

    route_data = route.model_dump()
    route_data["tenant_id"] = tenant_id
    route_data["created_by"] = user_id
    route_data["updated_by"] = user_id
    route_data["project_id"] = project_id
    # Convert schema model to core model
    core_route = CoreRoute.model_validate(route_data)
    route = await route_service.create_route(tenant_id, project_id, core_route)
    # Convert core model to schema model
    route_response = Route.model_validate(route.model_dump())
    return RouteResponse(data=route_response, message="Route created successfully")


@router.get("/{route_id}", response_model=RouteResponse)
async def get_route(
    project_id: UUID,
    route_id: UUID,
    route_service: RouteServiceDep,
):
    """Get a route by ID"""
    tenant_id, user_id = get_request_context()
    core_route = await route_service.get_route_by_id(tenant_id, project_id, route_id)
    if not core_route:
        raise HTTPException(status_code=404, detail="Route not found")
    # Convert core model to schema model
    route = Route.model_validate(core_route.model_dump())
    return RouteResponse(data=route, message="Route retrieved successfully")


@router.put("/{route_id}", response_model=RouteResponse)
async def update_route(
    project_id: UUID,
    route_id: UUID,
    route: RouteUpdate,
    route_service: RouteServiceDep,
):
    """Update a route"""
    tenant_id, user_id = get_request_context()
    route_data = route.model_dump()
    # Convert schema model to core model
    route_data["project_id"] = project_id
    route_data["tenant_id"] = tenant_id
    route_data["updated_by"] = user_id
    core_route = CoreRoute.model_validate(route_data)
    route = await route_service.update_route(tenant_id, project_id, route_id, core_route)
    # Convert core model to schema model
    route_response = Route.model_validate(route.model_dump())
    return RouteResponse(data=route_response, message="Route updated successfully")
