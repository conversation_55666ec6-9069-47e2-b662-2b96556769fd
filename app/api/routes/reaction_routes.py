from fastapi import APIRouter, HTTPException

from app.core.models.reaction import Reaction as CoreReaction
from app.api.dependencies import ReactionServiceDep
from app.api.schemas.reaction import (
    Reaction,
    ReactionCreate,
    ReactionUpdate,
    ReactionResponse,
    ReactionListResponse,
)
from app.common.request_context import get_request_context


router = APIRouter(
    prefix="/projects/{project_id}/routes/{route_id}/steps/{step_id}/reactions", tags=["Experimentation: Reaction Management"]
)


@router.get("", response_model=ReactionListResponse)
async def get_reactions(
    project_id: str,
    route_id: str,
    step_id: str,
    reaction_service: ReactionServiceDep,
    skip: int = 0,
    limit: int = 100,
):
    """Get all reactions for a step"""
    tenant_id, user_id = get_request_context()
    core_reactions = await reaction_service.get_reactions_by_step(step_id, skip, limit)
    # Convert core models to schema models
    reactions = [Reaction.model_validate(reaction.model_dump()) for reaction in core_reactions]
    total = len(reactions)  # In a real app, you'd get total from the service
    return ReactionListResponse(
        data=reactions, total=total, skip=skip, limit=limit, message="Reactions retrieved successfully"
    )


@router.post("", response_model=ReactionResponse)
async def create_reaction(
    project_id: str,
    route_id: str,
    step_id: str,
    reaction: ReactionCreate,
    reaction_service: ReactionServiceDep,
):
    """Create a new reaction"""
    tenant_id, user_id = get_request_context()
    # Convert schema model to core model
    core_reaction = CoreReaction.model_validate(reaction.model_dump())
    core_reaction.step_id = step_id
    core_reaction.tenant_id = tenant_id
    core_reaction.created_by = user_id
    core_reaction.updated_by = user_id
    reaction_id = await reaction_service.create_reaction(core_reaction)
    return ReactionResponse(data=reaction_id, message="Reaction created successfully")


@router.get("/{reaction_id}", response_model=ReactionResponse)
async def get_reaction(
    project_id: str,
    route_id: str,
    step_id: str,
    reaction_id: str,
    reaction_service: ReactionServiceDep,
):
    """Get a reaction by ID"""
    tenant_id, user_id = get_request_context()
    core_reaction = await reaction_service.get_reaction(reaction_id)
    if not core_reaction or core_reaction.step_id != step_id or core_reaction.tenant_id != tenant_id:
        raise HTTPException(status_code=404, detail="Reaction not found")
    # Convert core model to schema model
    reaction = Reaction.model_validate(core_reaction.model_dump())
    return ReactionResponse(data=reaction, message="Reaction retrieved successfully")


@router.put("/{reaction_id}", response_model=ReactionResponse)
async def update_reaction(
    project_id: str,
    route_id: str,
    step_id: str,
    reaction_id: str,
    reaction: ReactionUpdate,
    reaction_service: ReactionServiceDep,
):
    """Update a reaction"""
    tenant_id, user_id = get_request_context()
    # Convert schema model to core model
    core_reaction = CoreReaction.model_validate(reaction.model_dump())
    core_reaction.step_id = step_id
    core_reaction.tenant_id = tenant_id
    core_reaction.updated_by = user_id
    success = await reaction_service.update_reaction(reaction_id, core_reaction)
    if not success:
        raise HTTPException(status_code=404, detail="Reaction not found")
    return ReactionResponse(data=success, message="Reaction updated successfully")
