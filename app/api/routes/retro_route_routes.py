from typing import Optional, List
from uuid import UUID
from fastapi import APIRouter, HTTPException
from fastapi.params import Query
from app.api.dependencies import RetroRouteServiceDep
from app.api.schemas.common import SuccessResponse
from app.api.schemas.retro_route import (
    RetroRouteListResponse,
    RetroRouteFiltersResponse,
    RetroRouteFilters,
    AlternateRoutesResponse,
    RetroRouteDetailResponse,
    RetroRouteDetail,
)
from app.api.schemas.retro_route_review import (
    RetroRouteReview,
    RetroRouteReviewRequest,
    RetroRouteReviewResponse,
    RetroRouteReviewListResponse,
)
from app.common.request_context import get_request_context

router = APIRouter(prefix="/projects/{project_id}/retro-routes", tags=["AI: Retro Route Management"])


@router.get("", response_model=RetroRouteListResponse)
async def get_retro_routes(
    project_id: UUID,
    retro_route_service: RetroRouteServiceDep,
    status: Optional[str] = Query(None, description="Filter by route status (e.g. shortlist, reject)"),
    reactions: Optional[List[str]] = Query(None, description="List of reaction types to filter by"),
    reagents: Optional[List[str]] = Query(None, description="List of reagents to filter by"),
    building_blocks: Optional[List[str]] = Query(None, description="List of building blocks to filter by"),
    sort_by: str = Query("total_route_score", description="Sort by field: total_route_score, steps, cost, created_at"),
    sort_order: int = Query(-1, description="Sort order: 1 for ascending, -1 for descending"),
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(100, ge=1, le=100, description="Max number of items to return"),
):
    tenant_id, _ = get_request_context()

    paginated_routes, retro_route_generation_status = await retro_route_service.get_retro_routes(
        tenant_id=tenant_id,
        project_id=project_id,
        raw_reactants=building_blocks,
        sort_by=sort_by,
        sort_order=sort_order,
        skip=skip,
        limit=limit,
        status=status,
        reactions=reactions,
        reagents=reagents,
    )

    return RetroRouteListResponse(
        success=True,
        data=paginated_routes.data,
        total=paginated_routes.total,
        skip=skip,
        limit=limit,
        generation_status=retro_route_generation_status,
        message="All Routes retrieved successfully",
    )


@router.get("/filters", response_model=RetroRouteFiltersResponse)
async def get_route_filters(project_id: UUID, retro_route_service: RetroRouteServiceDep):
    tenant_id, _ = get_request_context()
    route_filters_core = await retro_route_service.get_retro_route_filters(tenant_id, project_id)
    if not route_filters_core:
        raise HTTPException(status_code=404, detail="No filters found")
    route_filters = RetroRouteFilters.model_validate(route_filters_core.model_dump())
    return RetroRouteFiltersResponse(success=True, data=route_filters, message="Filters retrieved successfully")


@router.get("/{retro_route_id}/alternatives", response_model=AlternateRoutesResponse)
async def get_alternative_routes(
    project_id: UUID,
    retro_route_id: UUID,
    retro_route_service: RetroRouteServiceDep,
):
    """Get alternative routes for a route"""
    tenant_id, _ = get_request_context()
    alternate_routes = await retro_route_service.get_alternative_routes(
        tenant_id=tenant_id,
        project_id=project_id,
        retro_route_id=retro_route_id,
    )
    if not alternate_routes:
        raise HTTPException(status_code=404, detail="No alternate routes found")

    return AlternateRoutesResponse(data=alternate_routes, message="Alternate routes retrieved successfully")


@router.post("/{retro_route_id}/reviews", response_model=RetroRouteReviewResponse)
async def save_review(
    project_id: UUID,
    retro_route_id: UUID,
    retro_route_review: RetroRouteReviewRequest,
    retro_route_service: RetroRouteServiceDep,
):
    """Update a route to the shortlist or reject or un-shortlist"""
    tenant_id, user_id = get_request_context()

    review = await retro_route_service.save_retro_route_review(
        tenant_id,
        project_id,
        retro_route_id,
        user_id,
        retro_route_review.review_text,
        retro_route_review.status,
    )
    review_response = RetroRouteReview.model_validate(review.model_dump())
    return RetroRouteReviewResponse(data=review_response, message="Review saved successfully")


@router.get("/reviews", response_model=RetroRouteReviewListResponse)
async def get_reviews(project_id: UUID, retro_route_service: RetroRouteServiceDep):
    """Get all reviews for a project"""
    tenant_id, user_id = get_request_context()

    reviews = await retro_route_service.get_retro_reviews_by_project(tenant_id, project_id)
    reviews_response = [RetroRouteReview.model_validate(review.model_dump()) for review in reviews]
    return RetroRouteReviewListResponse(data=reviews_response, message="Reviews retrieved successfully")


# This method should be the last one in the routes. dont add any other routes after this one
@router.get("/{retro_route_id}", response_model=RetroRouteDetailResponse)
async def get_route(project_id: UUID, retro_route_id: UUID, retro_route_service: RetroRouteServiceDep):
    tenant_id, _ = get_request_context()

    route_dict = await retro_route_service.get_route_details_by_id(
        tenant_id=tenant_id,
        project_id=project_id,
        retro_route_id=retro_route_id,
    )
    if not route_dict:
        raise HTTPException(status_code=404, detail="Route not found")
    
    # Validate the data against the schema
    route_detail = RetroRouteDetail.model_validate(route_dict)
    return RetroRouteDetailResponse(data=route_detail, message="Route Data retrieved successfully")
