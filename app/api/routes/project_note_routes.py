from typing import Optional
from uuid import UUID
from fastapi import APIRouter, HTTPException, Query

from app.api.dependencies import ProjectNoteServiceDep
from app.api.schemas.common import SuccessResponse
from app.api.schemas.project_note import (
    Note,
    NoteUpdate,
    SortByEnum,
    NoteType, ProjectNotesRequest, ProjectNotesResponseList,
)
from app.common.request_context import get_request_context
from app.core.models.project_note import ProjectNote

router = APIRouter(prefix="/projects/{project_id}/notes", tags=["Project Notes"])


@router.get("", response_model=SuccessResponse[ProjectNotesResponseList])
async def get_project_notes(
    project_id: UUID,
    project_note_service: ProjectNoteServiceDep,
    sort_by: Optional[SortByEnum] = Query(None, description="Sort by date, priority, or type"),
    note_type: Optional[NoteType] = Query(None, description="Filter by note type"),
):
    """
    Get all notes for a research workspace with optional sorting and filtering.

    - **sort_by**: Sort by 'date' (newest first), 'priority' (High > Medium > Low), or 'type' (Patent > RetroRoute/Pathway > Quick Note)
    - **note_type**: Filter by 'patent_claims', 'patent_reactions', 'pathways', or 'quicknotes'
    """
    tenant_id, user_id = get_request_context()

    notes = await project_note_service.get_project_notes(
        tenant_id, project_id, sort_by.value if sort_by else None, note_type.value if note_type else None
    )
    if notes is None:
        raise HTTPException(status_code=404, detail="Workspace not found")

    stats_obj = project_note_service._compute_note_stats(notes)
    stats_dict = stats_obj.model_dump()

    note_responses = [Note.model_validate(note) for note in notes]

    return SuccessResponse(
        success=True,
        message="Workspace notes retrieved successfully",
        data=ProjectNotesResponseList(project_id=project_id, stats=stats_dict, notes=note_responses)
    )


@router.post("", response_model=SuccessResponse[Note])
async def create_project_note(
    project_id: UUID,
    note: ProjectNotesRequest,
    project_note_service: ProjectNoteServiceDep,
):
    """
    Create a new note in the research workspace.
    """
    tenant_id, user_id = get_request_context()

    note_data = note.model_dump()
    note_data["project_id"] = project_id
    note_data["tenant_id"] = tenant_id
    note_data["created_by"] = user_id
    note_data["updated_by"] = user_id

    core_note = ProjectNote.model_validate(note_data)
    created_note = await project_note_service.create_project_note(tenant_id, project_id, user_id, core_note)
    note_response = Note.model_validate(created_note)
    return SuccessResponse(
        success=True, message="Note created successfully", data=note_response  # validated NoteResponse
    )


@router.put("/{note_id}", response_model=SuccessResponse[Note])
async def update_project_note(
    project_id: UUID,
    note_id: UUID,
    note: NoteUpdate,
    project_note_service: ProjectNoteServiceDep,
):
    """
    Update an existing note in the research workspace.
    """
    tenant_id, user_id = get_request_context()

    updated_note = await project_note_service.update_project_note(tenant_id, project_id, note_id, user_id, note)

    if not updated_note:
        raise HTTPException(status_code=404, detail="Note not found")

    note_response = Note.model_validate(updated_note)

    return SuccessResponse(message="Note updated successfully", data=note_response)  # validated NoteResponse


@router.delete("/{note_id}", response_model=SuccessResponse)
async def delete_project_note(
    project_id: UUID,
    note_id: UUID,
    project_note_service: ProjectNoteServiceDep,
):
    """
    Delete a note from the research workspace.
    """
    tenant_id, user_id = get_request_context()

    await project_note_service.delete_project_note(tenant_id, project_id, user_id, note_id)

    return SuccessResponse(message="Note deleted successfully", data=None)
