from datetime import datetime
from typing import List, Optional, Dict
from uuid import UUID
from enum import Enum
from pydantic import BaseModel, Field

from app.api.schemas.common import SuccessResponse


class RequestStatus(str, Enum):
    QUEUED = "QUEUED"
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"


class ProjectBase(BaseModel):
    """Base schema for project data"""

    compound_name: str = Field(..., description="Name of the compound to be synthesized")
    canonical_smiles: str = Field(..., description="Canonical SMILES notation of the compound")
    description: Optional[str] = Field(None, description="Optional long description or objective")
    tags: Optional[List[str]] = Field([], description="Optional tags for project classification")
    start_date: Optional[datetime] = Field(None, description="Start date of the project")
    end_date: Optional[datetime] = Field(None, description="End date of the project (nullable if ongoing)")
    owner: Optional[UUID] = Field(None, description="User ID or name of the project owner")
    owner_name: Optional[str] = Field(None, description="Name of the project owner")


class ProjectCreate(ProjectBase):
    """Schema for creating a new project"""

    status: str = Field("draft", description="Current status of the project")


class ProjectUpdate(BaseModel):
    """Schema for updating an existing project"""

    name: Optional[str] = Field(None, description="Human-readable project name")
    compound_name: Optional[str] = Field(None, description="Name of the compound to be synthesized")
    canonical_smiles: Optional[str] = Field(None, description="Canonical SMILES notation of the compound")
    description: Optional[str] = Field(None, description="Optional long description or objective")
    tags: Optional[List[str]] = Field([], description="Optional tags for project classification")
    status: Optional[str] = Field(None, description="Current status of the project")
    end_state: Optional[str] = Field(None, description="Final outcome or notes about project conclusion")
    owner: Optional[UUID] = Field(None, description="User ID or name of the project owner")
    start_date: Optional[datetime] = Field(None, description="Start date of the project")
    end_date: Optional[datetime] = Field(None, description="End date of the project (nullable if ongoing)")
    approved_by: Optional[UUID] = Field(None, description="User who approved the project")


class CollaboratorInfo(BaseModel):
    user_id: UUID
    user_name: str
    email: str
    role: str
    active: bool
    role_id : str
    permissions: List[str]


class Project(ProjectBase):
    """Schema for project response"""

    id: UUID = Field(..., description="Unique identifier for the project")
    # tenant_id: UUID = Field(..., description="Tenant ID that owns this project")
    status: str = Field(..., description="Current status of the project")
    molecule_image_url: str = Field(..., description="Blob URL of the molecule to be synthesized")
    shortlisted_claims_count: Optional[int] = Field(0, description="Number of shortlisted patent claims")
    shortlisted_routes_count: Optional[int] = Field(0, description="Number of shortlisted synthesis routes")
    # end_state: Optional[str] = Field(None, description="Final outcome or notes about project conclusion")
    # approved_by: Optional[UUID] = Field(None, description="User who approved the project")
    #created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")
    # created_by: Optional[UUID] = Field(None, description="User who created the project")
    #updated_by: Optional[UUID] = Field(None, description="User who last updated the project")
    experiments: str = Field("0/0", description="Count of experiments conducted in the project")
    pathway_generation_status: RequestStatus = Field(RequestStatus.QUEUED, description="Current status of pathway generation")
    patent_generation_status: RequestStatus = Field(RequestStatus.QUEUED, description="Current status of patent generation")
    collaborators: Dict[UUID, CollaboratorInfo] = Field(default_factory=dict, description="Dictionary of collaborators with user_id as key")

    class Config:
        from_attributes = True


class AddCollaboratorRequest(BaseModel):
    user_id: str
    user_name: str
    email: str
    role: Optional[str]
    role_id: Optional[str]
    permissions: Optional[List[str]]


class RemoveCollaboratorRequest(BaseModel):
    user_id: str


# Response models
class ProjectResponse(SuccessResponse[Project]):
    """Response model for a single project"""
    pass

class ProjectListResponse(SuccessResponse[List[Project]]):
    """Response schema for project list operations"""
    total: int = Field(..., description="Total number of projects")
    skip: int = Field(0, description="Number of projects skipped for pagination")
    limit: int = Field(100, description="Maximum number of projects to return per page")
