from datetime import datetime
from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel, Field, conint, confloat

from app.api.schemas.common import SuccessResponse, ErrorResponse


class StepBase(BaseModel):
    """Base schema for step data"""

    name: str = Field(..., description="Name of the step")
    description: Optional[str] = Field(None, description="Description of the step")
    rxn_string: str = Field(..., description="Reaction in RXN format (e.g., SMILES or RXN block)")
    rxn_type: str = Field(..., description="Descriptive reaction type (e.g., 'Chloride salt formation')")
    rxn_class: Optional[str] = Field(None, description="Reaction classification (e.g., 'Salt formation')")
    rxn_superclass: Optional[str] = Field(None, description="Reaction superclass (e.g., 'Separation and resolution')")
    notes: Optional[str] = Field(None, description="Optional notes or explanation from chemist")


class StepCreate(StepBase):
    """Schema for creating a new step"""

    route_id: UUID = Field(..., description="Reference to the parent synthesis route")
    step_number: conint(ge=1) = Field(..., description="The order of the step within the route")


class StepUpdate(BaseModel):
    """Schema for updating an existing step"""

    rxn_string: Optional[str] = Field(None, description="Reaction in RXN format")
    rxn_type: Optional[str] = Field(None, description="Descriptive reaction type")
    rxn_class: Optional[str] = Field(None, description="Reaction classification")
    rxn_superclass: Optional[str] = Field(None, description="Reaction superclass")
    certainty: Optional[confloat(ge=0, le=1)] = Field(None, description="Confidence score (0 to 1)")
    predicted_yield: Optional[confloat(ge=0, le=100)] = Field(None, description="Predicted yield percentage")
    experimental_yield: Optional[confloat(ge=0, le=100)] = Field(None, description="Actual observed yield percentage")
    notes: Optional[str] = Field(None, description="Optional notes or explanation")


class Step(StepBase):
    """Schema for step response"""

    id: UUID = Field(..., description="Unique identifier for the step")
    tenant_id: UUID = Field(..., description="Tenant ID that owns this step")
    project_id: UUID = Field(..., description="Project ID that owns this step")
    route_id: UUID = Field(..., description="Reference to the parent synthesis route")
    step_number: conint(ge=1) = Field(..., description="The order of the step within the route")
    certainty: Optional[confloat(ge=0, le=1)] = Field(None, description="Confidence score (0 to 1)")
    predicted_yield: Optional[confloat(ge=0, le=100)] = Field(None, description="Predicted yield percentage")
    experimental_yield: Optional[confloat(ge=0, le=100)] = Field(None, description="Actual observed yield percentage")
    created_by: Optional[UUID] = Field(None, description="User ID of the person who added the step")
    updated_by: Optional[UUID] = Field(None, description="User ID of the person who updated the step")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True


# Response models
class StepResponse(SuccessResponse[Step]):
    """Response model for single step"""

    pass


class StepListResponse(SuccessResponse[list[Step]]):
    """Response model for list of steps"""

    pass


class StepErrorResponse(ErrorResponse):
    """Error response model for step operations"""

    pass
