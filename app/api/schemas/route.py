from datetime import datetime
from typing import List, Optional
from uuid import UUID
from pydantic import BaseModel, Field, conint, confloat

from app.api.schemas.common import SuccessResponse, ErrorResponse


class Attachment(BaseModel):
    """Schema for file attachments"""

    name: str = Field(..., description="Name of the attached file")
    url: str = Field(..., description="URL to access the file")
    type: str = Field(..., description="Type of the file (e.g., 'pdf', 'image')")
    size: int = Field(..., description="Size of the file in bytes")
    uploaded_at: datetime = Field(..., description="Timestamp when the file was uploaded")
    uploaded_by: str = Field(..., description="User who uploaded the file")


class RouteBase(BaseModel):
    """Base schema for route data"""

    name: str = Field(..., description="Human-readable route name")
    description: Optional[str] = Field(None, description="Optional description of the route")
    team: Optional[str] = Field(None, description="Team responsible for the route")
    priority: Optional[str] = Field(None, description="Priority level of the route")
    tags: Optional[List[str]] = Field(None, description="Optional tags for route classification")


class RouteCreate(RouteBase):
    """Schema for creating a new route"""

    project_id: Optional[UUID] = Field(None, description="Reference to the parent project")


class RouteUpdate(BaseModel):
    """Schema for updating an existing route"""

    name: Optional[str] = Field(None, description="Human-readable route name")
    description: Optional[str] = Field(None, description="Optional description of the route")
    team: Optional[str] = Field(None, description="Team responsible for the route")
    priority: Optional[str] = Field(None, description="Priority level of the route")
    tags: Optional[List[str]] = Field(None, description="Optional tags for route classification")
    route_cost_in_usd: Optional[float] = Field(None, description="Estimated cost of the synthesis route in USD")
    certainty: Optional[confloat(ge=0, le=1)] = Field(None, description="Confidence score (0 to 1)")
    yield_percentage: Optional[confloat(ge=0, le=100)] = Field(
        None, description="Estimated or observed yield in percentage"
    )


class Route(RouteBase):
    """Schema for route response"""

    id: UUID = Field(..., description="Unique identifier for the route")
    project_id: UUID = Field(..., description="Reference to the parent project")
    tenant_id: UUID = Field(..., description="Tenant ID that owns this route")
    status: str = Field(..., description="Current status of the route")
    steps_count: conint(ge=0) = Field(..., description="Number of steps in the route")
    route_cost_in_usd: Optional[float] = Field(None, description="Estimated cost of the synthesis route in USD")
    certainty: Optional[confloat(ge=0, le=1)] = Field(None, description="Confidence score (0 to 1)")
    score: Optional[float] = Field(None, description="Composite score used for route ranking")
    rank: Optional[conint(ge=1)] = Field(None, description="Relative rank among all available routes")
    yield_percentage: Optional[confloat(ge=0, le=100)] = Field(
        None, description="Estimated or observed yield in percentage"
    )
    created_by: str = Field(..., description="User who created the route")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")
    approved_by: Optional[str] = Field(None, description="User who approved the route")
    approved_at: Optional[datetime] = Field(None, description="Timestamp of approval")
    attachments: Optional[List[Attachment]] = Field(None, description="Attached files")

    class Config:
        from_attributes = True


# Response models
class RouteResponse(SuccessResponse[Route]):
    """Response model for single route"""

    pass


class RouteListResponse(SuccessResponse[List[Route]]):
    """Response model for list of routes"""

    pass


class RouteErrorResponse(ErrorResponse):
    """Error response model for route operations"""

    pass
