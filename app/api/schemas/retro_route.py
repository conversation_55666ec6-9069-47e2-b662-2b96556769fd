from datetime import datetime
from typing import List, Optional, Union, get_args, get_origin
from uuid import UUID

from pydantic import BaseModel, Field, conint, model_validator, root_validator

from app.api.schemas.common import SuccessResponse
from app.api.schemas.project import RequestStatus
from app.core.models.retro_route import ReactionConfidence


class RetroRoute(BaseModel):
    """Base schema for route data"""

    id: UUID = Field(..., description="Unique identifier for the route")
    route_name: str = Field(..., description="Human-readable route name")
    status: Optional[str] = Field("Planned", description="status of the route")
    request_id: UUID = Field(..., description="Reference to the parent request")
    num_steps: conint(ge=0) = Field(..., description="Number of steps in the route")
    route_cost: Optional[float] = Field(None, description="Estimated cost of the synthesis route in USD")
    total_route_score: Optional[float] = Field(None, description="Composite score used for route ranking")
    raw_reactants: List[str] = Field(default_factory=list, description="Building blocks used in the route")
    route_reaction_img: Optional[str] = Field(None, description="Image URL for the route")
    tags: List[str] = Field(default_factory=list, description="Tags for route classification")
    reagents: List[str] = Field(default_factory=list, description="List of reagents used in the route")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")
    # Added only for review status and text
    review_text: Optional[str] = Field(None, description="Text of the review")

    class Config:
        from_attributes = True


class RetroRouteResponse(SuccessResponse[RetroRoute]):
    """Schema for route response"""

    pass


def extract_actual_type(annotation):
    origin = get_origin(annotation)
    if origin is Union:
        args = [arg for arg in get_args(annotation) if arg is not type(None)]
        return args[0] if args else annotation
    return annotation

class RetroRouteListResponse(SuccessResponse[List[RetroRoute]]):
    """Response schema for list of retro routes"""

    total: int = Field(..., description="Total number of routes")
    skip: int = Field(0, description="Number of routes skipped for pagination")
    limit: int = Field(100, description="Maximum number of routes to return per page")
    generation_status: Optional[RequestStatus] = Field(RequestStatus.QUEUED, description="Current status of retro routes generation")


# Detailed schemas for route details
class ReactionClass(BaseModel):
    """Schema for reaction classification"""
    rank: Optional[int] = None
    reaction_num: Optional[str] = None
    reaction_name: Optional[str] = None
    reaction_classnum: Optional[str] = None
    reaction_classname: Optional[str] = None
    reaction_superclassnum: Optional[str] = None
    reaction_superclassname: Optional[str] = None
    prediction_certainty: Optional[float] = None

    @model_validator(mode='before')
    @classmethod
    def replace_none_with_default(cls, values):
        for field_name, field in cls.model_fields.items():
            if values.get(field_name) is None:
                actual_type = extract_actual_type(field.annotation)
                if actual_type is float:
                    values[field_name] = 0.0
                elif actual_type is str:
                    values[field_name] = ""
        return values


class ReactionDetails(BaseModel):
    reactants_identified: Optional[List[str]] = None
    products_identified: Optional[List[str]] = None
    reaction_name: Optional[str] = None

    @model_validator(mode='before')
    @classmethod
    def replace_none_with_default(cls, values):
        for field_name, field in cls.model_fields.items():
            if values.get(field_name) is None:
                actual_type = extract_actual_type(field.annotation)
                if actual_type is float:
                    values[field_name] = 0.0
                elif actual_type is str:
                    values[field_name] = ""
        return values


class ReagentInfo(BaseModel):
    name: Optional[str] = None
    role: Optional[str] = None
    price_per_unit: Optional[float] = None
    currency: Optional[str] = None
    unit_basis: Optional[str] = None
    price_source: Optional[str] = None
    price_confidence: Optional[str] = None

    @model_validator(mode='before')
    @classmethod
    def replace_none_with_default(cls, values):
        for field_name, field in cls.model_fields.items():
            if values.get(field_name) is None:
                actual_type = extract_actual_type(field.annotation)
                if actual_type is float:
                    values[field_name] = 0.0
                elif actual_type is str:
                    values[field_name] = ""
        return values


class ReactionConditions(BaseModel):
    temperature_from_dataset: Optional[str] = None
    time_from_dataset: Optional[str] = None
    yield_from_dataset: Optional[str] = None
    atmosphere_llm_or_dataset: Optional[str] = None

    @model_validator(mode='before')
    @classmethod
    def replace_none_with_default(cls, values):
        for field_name, field in cls.model_fields.items():
            if values.get(field_name) is None:
                actual_type = extract_actual_type(field.annotation)
                if actual_type is float:
                    values[field_name] = 0.0
                elif actual_type is str:
                    values[field_name] = ""
        return values


class SafetyAndNotes(BaseModel):
    safety: Optional[str] = None
    notes: Optional[str] = None

    @model_validator(mode='before')
    @classmethod
    def replace_none_with_default(cls, values):
        for field_name, field in cls.model_fields.items():
            if values.get(field_name) is None:
                actual_type = extract_actual_type(field.annotation)
                if actual_type is float:
                    values[field_name] = 0.0
                elif actual_type is str:
                    values[field_name] = ""
        return values


class OtherInformation(BaseModel):
    """Schema for other reaction information"""
    error_message: Optional[str] = None
    reaction_smiles_interpreted: Optional[str] = None
    reaction_details: Optional[ReactionDetails] = None
    reagents_and_solvents: Optional[List[ReagentInfo]] = None
    reaction_conditions: Optional[ReactionConditions] = None
    safety_and_notes: Optional[SafetyAndNotes] = None
    procedure_steps: Optional[List[str]] = None
    experimental_data_source_note: Optional[str] = None
    visualization_path: Optional[str] = None

    @model_validator(mode='before')
    @classmethod
    def replace_none_with_default(cls, values):
        for field_name, field in cls.model_fields.items():
            if values.get(field_name) is None:
                actual_type = extract_actual_type(field.annotation)
                if actual_type is float:
                    values[field_name] = 0.0
                elif actual_type is str:
                    values[field_name] = ""
        return values


class Reactant(BaseModel):
    smiles: Optional[str] = None
    name: Optional[str] = None
    synthesis_score: Optional[float] = None
    is_terminal: Optional[str] = None

    @model_validator(mode='before')
    @classmethod
    def replace_none_with_default(cls, values):
        for field_name, field in cls.model_fields.items():
            if values.get(field_name) is None:
                actual_type = extract_actual_type(field.annotation)
                if actual_type is float:
                    values[field_name] = 0.0
                elif actual_type is str:
                    values[field_name] = ""
        return values


class RouteStep(BaseModel):
    """Schema for a single route step"""
    step: Optional[int] = None
    reaction_id: Optional[str] = None
    reaction_string: Optional[str] = None
    reaction_name: Optional[str] = None
    retro_smiles: Optional[str] = None
    reagents: Optional[str] = None
    forward_prediction: Optional[str] = None
    prob_forward_1: Optional[float] = None
    prob_forward_2: Optional[float] = None
    score: Optional[float] = None
    step_cost: Optional[float] = None
    rxn_class: Optional[ReactionClass] = None
    reaction_confidence: Optional[ReactionConfidence] = None
    reaction_smiles_img: Optional[str] = None
    other_information: Optional[OtherInformation] = None
    reactants: Optional[List[Reactant]] = None

    @model_validator(mode='before')
    @classmethod
    def replace_none_with_default(cls, values):
        for field_name, field in cls.model_fields.items():
            if values.get(field_name) is None:
                actual_type = extract_actual_type(field.annotation)
                if actual_type is float:
                    values[field_name] = 0.0
                elif actual_type is str:
                    values[field_name] = ""
        return values


class RetroRouteDetail(BaseModel):
    """Detailed schema for retro route data"""
    id: Optional[str] = None
    unique_id: Optional[str] = None
    request_id: Optional[str] = None
    route_id: Optional[int] = None
    target_smiles: Optional[str] = None
    route_name: Optional[str] = None
    num_steps: Optional[int] = None
    total_route_score: Optional[float] = None
    route_reaction_img: Optional[str] = None
    raw_reactants: Optional[List[str]] = None
    status: Optional[str] = None
    route_cost: Optional[float] = None
    tags: Optional[List[str]] = None
    data: Optional[List[RouteStep]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @model_validator(mode='before')
    @classmethod
    def replace_none_with_default(cls, values):
        for field_name, field in cls.model_fields.items():
            if values.get(field_name) is None:
                actual_type = extract_actual_type(field.annotation)
                if actual_type is float:
                    values[field_name] = 0.0
                elif actual_type is str:
                    values[field_name] = ""
        return values


class RetroRouteDetailResponse(SuccessResponse[RetroRouteDetail]):
    """Response schema for detailed retro route data"""
    pass


class AlternateRoutes(BaseModel):
    count: int
    routes: List[RetroRoute]

class AlternateRoutesData(BaseModel):
    fewer_steps_paths: AlternateRoutes
    lower_cost_paths: AlternateRoutes
    higher_score_paths: AlternateRoutes

class AlternateRoutesResponse(BaseModel):
    data: AlternateRoutesData
    message: str


class RetroRouteFilters(BaseModel):
    """Schema for filter response"""

    reactions: List[str]
    reagents: List[str]
    building_blocks: List[str]


class RetroRouteFiltersResponse(SuccessResponse[RetroRouteFilters]):
    pass
