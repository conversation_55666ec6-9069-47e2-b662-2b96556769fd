from datetime import datetime
from uuid import UUID

from typing import List, Optional

from pydantic import BaseModel, <PERSON>, model_validator

from app.core.models.retro_route_review import RetroRouteReviewStatus
from app.api.schemas.common import SuccessResponse


class RetroRouteReviewRequest(BaseModel):
    status: RetroRouteReviewStatus
    review_text: Optional[str] = Field("", description="Review text for the route")

    @model_validator(mode="after")
    def check_review_text_required_for_rejected(self):
        if self.status == RetroRouteReviewStatus.REJECTED and not self.review_text:
            raise ValueError("review_text is required when status is 'rejected'")
        return self


class RetroRouteReview(BaseModel):
    id: UUID
    project_id: UUID
    retro_route_id: UUID
    status: RetroRouteReviewStatus
    review_text: str
    created_at: datetime
    updated_at: datetime


class RetroRouteReviewResponse(SuccessResponse[RetroRouteReview]):
    """Response model for a single project"""

    pass


class RetroRouteReviewListResponse(SuccessResponse[List[RetroRouteReview]]):
    """Response model for a list of projects"""

    pass
