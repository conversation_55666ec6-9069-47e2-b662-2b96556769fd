from datetime import datetime
from enum import Enum
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from app.api.schemas.common import SuccessResponse


# TODO: All these needs to updated properly


class SortByEnum(str, Enum):
    """Enum for sort options"""

    DATE = "date"
    PRIORITY = "priority"
    TYPE = "type"


class NoteType(str, Enum):
    """Enum for note type filter options"""

    PATENT_CLAIMS = "patent_claims"
    PATENT_REACTIONS = "patent_reactions"
    PATHWAYS = "pathways"
    QUICK_NOTES = "quick_notes"

class Priority(str, Enum):
    """Enum for priority"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class ProjectNotesRequest(BaseModel):
    """Schema for creating a new note"""

    name: str = Field(None, description="Human-readable note name")
    note: str = Field(..., description="Content of the note")
    description: Optional[str] = Field(None, description="Optional description of the note")
    note_type: NoteType = Field(
        ..., description="Type of the note Empty for route-related notes,  patent-related notes, project-related notes"
    )
    note_type_id: Optional[str] = Field(default="", description="ID of route/patent")
    priority: Priority = Field(..., description="Priority of the note")
    user_name: str = Field(None, description="Name of the user creating the note")



class Note(BaseModel):
    id: UUID = Field(..., alias="_id")
    name: Optional[str] = None
    description: Optional[str] = Field(None, description="Description of notes")
    note: Optional[str] = None
    note_type: NoteType
    note_type_id: Optional[str] = None
    priority: Priority = Priority.MEDIUM
    project_id: UUID
    tenant_id: UUID
    created_by: Optional[UUID] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    user_name: str = Field(None, description="Name of the user who created the note")

    # Optional: If you want to allow Pydantic to use attributes for ORM-style models
    model_config = {
        "populate_by_name": True,  # allow mapping from aliases
        "from_attributes": True,  # if using ORM instances
    }


class ProjectNoteStats(BaseModel):
    patent_claims: int
    patent_reactions: int
    pathways: int
    quick_notes: int
    high_priority: int
    total_notes: int


class ProjectNotesResponseList(BaseModel):
    project_id: UUID
    stats: ProjectNoteStats
    notes: List[Note]

class NoteUpdate(BaseModel):
    note: str = Field(..., description="Content of the note")

