from typing import TypeV<PERSON>, Generic, Optional, Dict, Any, List
from pydantic import BaseModel, Field
from uuid import UUID

# Generic type for the data payload
T = TypeVar("T")


class ErrorDetails(BaseModel):
    """Schema for error details"""

    code: str = Field(..., description="Error code identifying the type of error")
    message: str = Field(..., description="Human readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error context")


class Error(BaseModel):
    """Schema for error response"""

    error: ErrorDetails


class SuccessResponse(BaseModel, Generic[T]):
    """Schema for successful response with data"""

    success: bool = Field(True, description="Indicates if the request was successful")
    data: T = Field(..., description="Response data")
    message: str = Field(..., description="Success message")
    trace_id: Optional[str] = Field(None, description="Trace ID for the request")


class ErrorResponse(BaseModel):
    """Schema for error response"""

    success: bool = Field(False, description="Indicates if the request was successful")
    error: ErrorDetails = Field(..., description="Error details")
    trace_id: Optional[str] = Field(None, description="Trace ID for the request")


# Common error codes
class ErrorCode:
    NOT_FOUND = "NOT_FOUND"
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    INTERNAL_ERROR = "INTERNAL_ERROR"
    ALREADY_EXISTS = "ALREADY_EXISTS"
    INVALID_REQUEST = "INVALID_REQUEST"


class PaginatedResponse(BaseModel, Generic[T]):
    """Paginated response model"""

    success: bool = True
    data: List[T]
    total: int
    skip: int
    limit: int
