from typing import List, Optional
from pydantic import BaseModel, Field
from app.api.schemas.common import SuccessResponse

class WeeklyStatus(BaseModel):
    week: str
    week_end_date: str
    no_of_patent_notes: int
    no_of_ai_pathway_shortlisted: int

class ResearchStatus(BaseModel):
    total_projects: int
    in_progress_projects: int
    completed_projects: int

class TopContributor(BaseModel):
    researcher_name: str
    project_name: str
    summary: str

class ResearchOverviewData(BaseModel):
    weekly_status: List[WeeklyStatus]
    research_status: ResearchStatus
    top_contributors: List[TopContributor]

class ResearchOverviewResponse(SuccessResponse[ResearchOverviewData]):
    pass 