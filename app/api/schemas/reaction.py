from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field, confloat

from app.api.schemas.common import SuccessResponse, ErrorResponse


class Identifier(BaseModel):
    """Schema for reaction identifiers"""

    type: str = Field(..., description="Type of the identifier")
    value: str = Field(..., description="Value of the identifier")
    details: Optional[str] = Field(None, description="Additional details about the identifier")


class Amount(BaseModel):
    """Schema for component amounts"""

    value: float = Field(..., description="Numerical value")
    units: str = Field(..., description="Units (e.g., MILLIMOLE, MICROLITER)")


class ComponentAmount(BaseModel):
    """Schema for component amount details"""

    moles: Optional[Amount] = Field(None, description="Amount in moles")
    volume: Optional[Amount] = Field(None, description="Amount in volume")
    volume_includes_solutes: Optional[bool] = Field(None, description="Whether the volume includes the solutes")


class Preparation(BaseModel):
    """Schema for component preparations"""

    type: str = Field(..., description="Type of preparation (e.g., DRIED)")


class Component(BaseModel):
    """Schema for reaction components"""

    identifiers: List[Identifier] = Field(..., description="Component identifiers")
    amount: Optional[ComponentAmount] = Field(None, description="Amount of the component")
    reaction_role: str = Field(..., description="Role in the reaction")
    is_limiting: Optional[bool] = Field(None, description="Whether this component is limiting")
    preparations: Optional[List[Preparation]] = Field(None, description="Preparations applied to the component")


class AdditionSpeed(BaseModel):
    """Schema for addition speed details"""

    type: str = Field(..., description="Type of addition speed (e.g., ALL_AT_ONCE)")
    details: Optional[str] = Field(None, description="Additional details about the addition")


class Input(BaseModel):
    """Schema for reaction inputs"""

    input_name: str = Field(..., description="Name of the input")
    components: List[Component] = Field(..., description="Components in this input")
    addition_order: Optional[int] = Field(None, description="Order in which this input is added")
    addition_speed: Optional[AdditionSpeed] = Field(None, description="Speed of addition")


class VesselMaterial(BaseModel):
    """Schema for vessel material"""

    type: str = Field(..., description="Material of the vessel (e.g., GLASS)")


class VesselAttachment(BaseModel):
    """Schema for vessel attachments"""

    type: str = Field(..., description="Type of attachment (e.g., CAP)")


class Vessel(BaseModel):
    """Schema for reaction vessel"""

    type: str = Field(..., description="Type of vessel (e.g., VIAL)")
    material: Optional[VesselMaterial] = Field(None, description="Material of the vessel")
    attachments: Optional[List[VesselAttachment]] = Field(None, description="Vessel attachments")


class Environment(BaseModel):
    """Schema for reaction environment"""

    type: str = Field(..., description="Type of environment (e.g., AIR)")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional environment details")


class Setup(BaseModel):
    """Schema for reaction setup"""

    vessel: Optional[Vessel] = Field(None, description="Reaction vessel details")
    environment: Optional[Environment] = Field(None, description="Reaction environment")


class ReactionBase(BaseModel):
    """Base schema for reaction data"""

    step_id: UUID = Field(..., description="Reference to the parent step")
    identifiers: List[Identifier] = Field(..., description="Identifiers for the reaction")
    inputs: List[Input] = Field(..., description="Input components used in the reaction")
    setup: Optional[Setup] = Field(None, description="Reaction setup details")


class ReactionCreate(ReactionBase):
    """Schema for creating a new reaction"""

    pass


class ReactionUpdate(BaseModel):
    """Schema for updating an existing reaction"""

    identifiers: Optional[List[Identifier]] = Field(None, description="Identifiers for the reaction")
    inputs: Optional[List[Input]] = Field(None, description="Input components used in the reaction")
    setup: Optional[Setup] = Field(None, description="Reaction setup details")


class Reaction(ReactionBase):
    """Schema for reaction response"""

    id: UUID = Field(..., description="Unique identifier for the reaction")
    tenant_id: UUID = Field(..., description="Tenant ID that owns this reaction")
    project_id: UUID = Field(..., description="Project ID that owns this reaction")
    route_id: UUID = Field(..., description="Route ID that owns this reaction")
    step_id: UUID = Field(..., description="Step ID that owns this reaction")
    created_by: str = Field(..., description="User who created the reaction")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")

    class Config:
        from_attributes = True


# Response models
class ReactionResponse(SuccessResponse[Reaction]):
    """Response model for single reaction"""

    pass


class ReactionListResponse(SuccessResponse[List[Reaction]]):
    """Response model for list of reactions"""

    pass


class ReactionErrorResponse(ErrorResponse):
    """Error response model for reaction operations"""

    pass
