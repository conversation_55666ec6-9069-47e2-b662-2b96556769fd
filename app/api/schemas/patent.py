from enum import Enum
from typing import List, Optional, Dict
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, Field
from app.api.schemas.common import SuccessResponse
from app.api.schemas.project import RequestStatus


class Claim(BaseModel):
    id: str
    text: str
    tag: str


class DependentClaim(Claim):
    depends_on: str


class PatentSortBy(str, Enum):
    RELEVANCY_SCORE = "relevancyScore"
    PUBLICATION_DATE = "publication_date"


class SortOrder(str, Enum):
    ASCENDING = "asc"
    DESCENDING = "desc"


class SimilarPatent(BaseModel):
    id: UUID
    patent_number: str
    tags: List[str]
    claim_tags: List[str]
    title: str
    publication_date: Optional[datetime]
    assignees: List[str]
    relevancy_score: float


class PatentResponse(BaseModel):
    id: UUID
    patent_number: str
    tags: List[str]
    claim_tags: List[str]
    claim_summary: str
    title: str
    key_reactions: List[str]
    publication_date: Optional[datetime]
    assignees: List[str]
    relevancy_score: float


class PatentDetailResponse(BaseModel):
    id: UUID
    patent_number: str
    tags: List[str]
    claim_tags: List[str]
    title: str
    abstract: str
    claim_summary: str
    key_reactions: List[str]
    similar_patents: List[SimilarPatent]
    independent_claims: List[Claim]
    dependent_claims: List[DependentClaim]
    inventors: List[str]
    filing_date: Optional[datetime]
    publication_date: Optional[datetime]
    assignees: List[str]
    pdf_url: str
    relevancy_score: float


class PatentListResponse(SuccessResponse[List[PatentResponse]]):
    """Response schema for project list operations"""
    total: int = Field(..., description="Total number of patents")
    skip: int = Field(0, description="Number of patents skipped for pagination")
    limit: int = Field(100, description="Maximum number of patents to return per page")
    generation_status: Optional[RequestStatus] = Field(RequestStatus.QUEUED, description="Current status of patent generation")


class PatentInputType(str, Enum):
    NUMBER = "patent_number"
    URL = "patent_url"


class PatentProcessingRequest(BaseModel):
    compound_name: str
    input_type: PatentInputType
    input_string: str


class PatentFilterResponse(BaseModel):
    reaction_types: List[str]
    claim_types: List[str]
    filing_companies: List[str]
    patent_offices: Dict[str, str]
