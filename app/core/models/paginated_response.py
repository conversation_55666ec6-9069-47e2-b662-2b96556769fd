from typing import Generic, TypeVar, List

T = TypeVar("T")


class PaginatedResponse(Generic[T]):
    """
    A generic class for paginated responses.
    """

    data: List[T]
    total: int
    skip: int
    limit: int

    def __init__(self, data: List[T], total: int, skip: int, limit: int):
        self.data = data
        self.total = total
        self.skip = skip
        self.limit = limit

    class Config:
        from_attributes = True
