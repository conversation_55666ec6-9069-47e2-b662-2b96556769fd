from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import Field, BaseModel

from .base import CoreBaseModel
from .request_log import RequestStatus


class ProjectStatus(str, Enum):
    """Project status enumeration"""
    DRAFT = "draft"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ON_HOLD = "on_hold"
    CANCELLED = "cancelled"


class NoteType(str, Enum):
    """Enum for note type filter options"""

    PATENT_CLAIMS = "patent_claims"
    PATENT_REACTIONS = "patent_reactions"
    PATHWAYS = "pathways"
    QUICK_NOTES = "quick_notes"


class Collaborator(BaseModel):
    user_id: str
    user_name: str
    email: Optional[str] = None
    role: Optional[str] = None
    role_id: Optional[str] = None
    active: bool = True
    permissions: Optional[List[str]] = Field(default_factory=list)


class Project(CoreBaseModel):
    tenant_id: UUID
    owner: Optional[UUID] = None
    owner_name: Optional[str] = None
    compound_name: str
    canonical_smiles: str
    molecule_image_url: Optional[str] = None
    shortlisted_claims_count: Optional[int] = 0
    shortlisted_routes_count: Optional[int] = 0
    start_date: Optional[datetime] = None
    status: ProjectStatus = Field(..., description="Current status of the project")
    end_date: Optional[datetime] = None
    end_state: Optional[str] = None
    approved_by: Optional[UUID] = None
    tags: Optional[List[str]] = Field(default_factory=list)
    description: Optional[str] = None
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None
    pathway_generation_status: Optional[RequestStatus] = Field(RequestStatus.QUEUED, description="Current status of pathway generation")
    patent_generation_status: Optional[RequestStatus] = Field(RequestStatus.QUEUED, description="Current status of patent generation")
    collaborators: Dict[str, Collaborator] = Field(default_factory=dict, description="Dictionary of collaborators with user_id as key")

    # create a list of keys that are not allowed to be updated
    @classmethod
    def get_keys_not_allowed_to_update(cls):
        return ["id", "tenant_id", "created_at", "created_by"]
