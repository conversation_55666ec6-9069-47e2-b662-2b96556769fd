from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from enum import Enum
from .base import CoreBaseModel


class RetroRouteReviewStatus(str, Enum):
    """Retro route review status enumeration"""

    SHORTLISTED = "shortlisted"
    REJECTED = "rejected"
    PLANNED = "planned" # Planned which is neither shortlisted nor rejected


class RetroRouteReview(CoreBaseModel):
    id: Optional[UUID] = None
    tenant_id: UUID
    project_id: UUID
    retro_route_id: UUID
    status: RetroRouteReviewStatus
    review_text: str
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @classmethod
    def get_keys_not_allowed_to_update(cls):
        return ["id", "tenant_id", "created_at", "created_by"]
