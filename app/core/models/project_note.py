from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import Field, HttpUrl
from .base import CoreBaseModel
from uuid import UUID


class ProjectNote(CoreBaseModel):
    id: Optional[UUID] = None
    tenant_id: UUID
    project_id: UUID
    name: str
    note: str
    description: Optional[str] = ""
    note_type: str
    note_type_id: Optional[str] = ""
    priority: Optional[str] = ""
    created_by: UUID
    user_name: str

    class Config:
        validate_by_name = True
        arbitrary_types_allowed = True


class ProjectNoteStats(CoreBaseModel):
    patent_claims: int
    patent_reactions: int
    pathways: int
    quick_notes: int
    high_priority: int
    total_notes: int
