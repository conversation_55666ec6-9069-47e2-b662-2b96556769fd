from datetime import datetime
from typing import List, Optional, Dict
from uuid import UUID
from pydantic import BaseModel
from enum import Enum


class Claim(BaseModel):
    id: str
    text: str
    tag: str


class DependentClaim(Claim):
    depends_on: str


class PatentSortBy(str, Enum):
    RELEVANCY_SCORE = "relevancyScore"
    PUBLICATION_DATE = "publication_date"

class PatentTag(str, Enum):
    WELL_STUDIED = "Well Studied"
    LITERATURE_RICH = "Literature Rich"
    EMERGING_RESEARCH = "Emerging Research"
    UNDER_RESEARCHED = "Under-Researched"


class SortOrder(str, Enum):
    ASCENDING = "asc"
    DESCENDING = "desc"


class SimilarPatent(BaseModel):
    id: UUID
    patent_number: str
    tags: List[str]
    claim_tags: List[str]
    title: str
    publication_date: Optional[datetime]
    assignees: List[str]
    relevancy_score: float


class Patent(BaseModel):
    id: UUID
    patent_number: str
    request_id: UUID
    title: str
    abstract: str
    tags: List[str]
    claim_tags: List[str]
    key_reactions: List[str]
    claim_summary: str
    publication_date: Optional[datetime]
    filing_date: Optional[datetime]
    similar_patents: List[SimilarPatent]
    inventors: List[str]
    assignees: List[str]
    independent_claims: List[Claim]
    dependent_claims: List[DependentClaim]
    pdf_url: str
    relevancy_score: float


class PatentInputType(str, Enum):
    NUMBER = "patent_number"
    URL = "patent_url"


class PatentTaskData(BaseModel):
    compound_name: str
    input_type: PatentInputType
    input_string: str


class PatentFilter(BaseModel):
    reaction_types: List[str]
    claim_types: List[str]
    filing_companies: List[str]
    patent_offices: Dict[str, str]
