from typing import Optional, List, Dict
from datetime import datetime
from pydantic import Field, HttpUrl
from .base import CoreBaseModel
from uuid import UUID


class Attachment(CoreBaseModel):
    filename: str
    url: HttpUrl
    uploaded_by: str
    uploaded_at: datetime
    type: str = Field(..., description="Type of attachment")
    name: str = Field(..., description="Name of the attachment")
    size: Optional[int] = Field(None, description="Size of the attachment in bytes")
    mime_type: Optional[str] = Field(None, description="MIME type of the attachment")


class Route(CoreBaseModel):
    tenant_id: UUID
    project_id: UUID
    name: str
    description: Optional[str] = None
    team: Optional[str] = None
    status: str = Field(default="planned", pattern="^(planned|in_progress|completed|abandoned)$")
    priority: Optional[str] = Field(default="medium", pattern="^(low|medium|high)$")
    steps_count: int = Field(default=0, ge=0)
    route_cost_in_usd: Optional[float] = None
    certainty: Optional[float] = Field(None, ge=0, le=1)
    score: Optional[float] = None
    rank: Optional[int] = Field(None, ge=1)
    tags: List[str] = Field(default_factory=list)
    yield_value: Optional[float] = Field(None, ge=0, le=100, alias="yield")
    attachments: List[Attachment] = Field(default_factory=list)
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None
    approved_by: Optional[UUID] = None
    approved_at: Optional[datetime] = None

    @classmethod
    def get_keys_not_allowed_to_update(cls):
        return ["id", "tenant_id", "project_id", "created_at", "created_by"]
