from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from enum import Enum

from pydantic import Field, BaseModel

from .base import CoreBaseModel


class RetroRouteFilters(BaseModel):
    reactions: List[str]
    reagents: List[str]
    building_blocks: List[str]


class ReactionConfidence(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class RouteStatus(str, Enum):
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"


class RetroRoute(CoreBaseModel):
    request_id: UUID
    route_id: Optional[int] = None
    unique_id: Optional[str] = None
    route_name: str
    status: str = Field(default="planned", pattern="^(planned|rejected|shortlisted)$")
    num_steps: int = Field(default=0, ge=0)
    route_cost: Optional[float] = None
    certainty: Optional[float] = Field(None, ge=0, le=1)
    total_route_score: Optional[float] = None
    tags: List[str] = Field(default_factory=list)
    reagents: List[str] = Field(None, description="Reagents")
    reactions: List[str] = Field(None, description="Reactions")
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None
    raw_reactants: List[str] = Field(None, description="Building blocks used in the route")
    route_reaction_img: Optional[str] = Field(None, description="Image URL for the route")
    data: Optional[List[Any]] = None
    review_text : Optional[str] = None
    reaction_confidence: Optional[ReactionConfidence] = Field(None, description="Confidence level of the reaction (low, medium, high)")

    @classmethod
    def get_keys_not_allowed_to_update(cls):
        return ["id", "tenant_id", "project_id", "created_at", "created_by"]
