from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field
from uuid import UUID


class CoreBaseModel(BaseModel):
    id: Optional[UUID] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        arbitrary_types_allowed = True

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        for key, value in data.items():
            if isinstance(value, UUID):
                data[key] = str(value)
        # remove all keys with value None
        data = {k: v for k, v in data.items() if v is not None}
        return data
