from typing import List
from pydantic import BaseModel, Field

class WeeklyStatus(BaseModel):
    week: str = Field(..., description="Week number")
    week_end_date: str = Field(..., description="End date of the week")
    no_of_patent_notes: int = Field(..., description="Number of patent notes")
    no_of_ai_pathway_shortlisted: int = Field(..., description="Number of AI pathway shortlisted")

class ResearchStatus(BaseModel):
    total_projects: int = Field(..., description="Total number of projects")
    in_progress_projects: int = Field(..., description="Number of in-progress projects")
    completed_projects: int = Field(..., description="Number of completed projects")

class TopContributor(BaseModel):
    researcher_name: str = Field(..., description="Name of the researcher")
    project_name: str = Field(..., description="Name of the project")
    summary: str = Field(..., description="Summary of contribution")

class ResearchOverview(BaseModel):
    weekly_status: List[WeeklyStatus]
    research_status: ResearchStatus
    top_contributors: List[TopContributor] 