from typing import Optional
from datetime import datetime
from uuid import UUID
from pydantic import Field
from .base import CoreBaseModel


class Step(CoreBaseModel):
    tenant_id: UUID
    project_id: UUID
    route_id: UUID
    name: str
    description: Optional[str] = None
    step_number: int = Field(..., ge=1)
    rxn_string: str
    rxn_type: str
    rxn_class: Optional[str] = None
    rxn_superclass: Optional[str] = None
    certainty: Optional[float] = Field(None, ge=0, le=1)
    predicted_yield: Optional[float] = Field(None, ge=0, le=100)
    experimental_yield: Optional[float] = Field(None, ge=0, le=100)
    notes: Optional[str] = None
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None

    @classmethod
    def get_keys_not_allowed_to_update(cls):
        return ["id", "tenant_id", "project_id", "route_id", "created_at", "created_by"]
