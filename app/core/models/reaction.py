from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import Field, BaseModel
from .base import CoreBaseModel
from uuid import UUID


class Identifier(BaseModel):
    """Schema for reaction identifiers"""

    type: str = Field(..., description="Type of the identifier")
    value: str = Field(..., description="Value of the identifier")
    details: Optional[str] = Field(None, description="Additional details about the identifier")


class Amount(BaseModel):
    """Base schema for amount data"""

    value: float = Field(..., description="Numeric value of the amount")
    unit: str = Field(..., description="Unit of measurement")


class VolumeAmount(Amount):
    """Schema for volume amounts"""

    pass


class ComponentAmount(Amount):
    """Schema for component amounts"""

    pass


class Preparation(BaseModel):
    """Schema for component preparation details"""

    method: str = Field(..., description="Preparation method")
    details: Optional[str] = Field(None, description="Additional preparation details")


class Component(BaseModel):
    """Schema for reaction components"""

    name: str = Field(..., description="Name of the component")
    role: str = Field(..., description="Role in the reaction (e.g., 'reactant', 'catalyst')")
    amount: ComponentAmount = Field(..., description="Amount of the component")
    preparation: Optional[Preparation] = Field(None, description="Preparation details")


class Input(BaseModel):
    """Schema for reaction inputs"""

    component: Component = Field(..., description="Component details")
    volume: Optional[VolumeAmount] = Field(None, description="Volume details")


class VesselAttachment(BaseModel):
    """Schema for vessel attachments"""

    type: str = Field(..., description="Type of attachment")
    details: Optional[str] = Field(None, description="Additional attachment details")


class VesselMaterial(BaseModel):
    """Schema for vessel materials"""

    type: str = Field(..., description="Type of material")
    details: Optional[str] = Field(None, description="Additional material details")


class Vessel(BaseModel):
    """Schema for reaction vessel"""

    type: str = Field(..., description="Type of vessel")
    material: VesselMaterial = Field(..., description="Vessel material")
    attachments: Optional[List[VesselAttachment]] = Field(None, description="Vessel attachments")


class Environment(BaseModel):
    """Schema for reaction environment"""

    temperature: Optional[float] = Field(None, description="Temperature in Celsius")
    pressure: Optional[float] = Field(None, description="Pressure in bar")
    atmosphere: Optional[str] = Field(None, description="Atmosphere type (e.g., 'air', 'nitrogen')")


class Setup(BaseModel):
    """Schema for reaction setup"""

    vessel: Vessel = Field(..., description="Reaction vessel")
    environment: Environment = Field(..., description="Reaction environment")


class Temperature(BaseModel):
    value: float
    units: str


class TemperatureControl(BaseModel):
    setpoint: Optional[Temperature] = None


class PressureControl(BaseModel):
    type: str


class Stirring(BaseModel):
    type: str


class Conditions(BaseModel):
    temperature: Optional[TemperatureControl] = None
    pressure: Optional[PressureControl] = None
    stirring: Optional[Stirring] = None


class Reaction(CoreBaseModel):
    tenant_id: UUID
    step_id: UUID
    identifiers: List[Identifier] = Field(default_factory=list)
    inputs: List[Input] = Field(default_factory=list)
    setup: Optional[Setup] = None
    conditions: Optional[Conditions] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None

    @classmethod
    def get_keys_not_allowed_to_update(cls):
        return ["id", "tenant_id", "step_id", "created_at", "created_by"]
