from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from .base import CoreBaseModel


class RequestType(str, Enum):
    RETRO = "retro"
    PATENT = "patent"


class RequestStatus(str, Enum):
    QUEUED = "QUEUED"
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

class RequestLog(CoreBaseModel):
    tenant_id: UUID
    project_id: UUID
    user_id: UUID
    request_type: RequestType
    status: RequestStatus
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    error_message: Optional[str] = None
