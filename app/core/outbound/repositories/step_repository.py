from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from uuid import UUID
from app.core.models.step import Step


class StepRepository(ABC):
    """Interface for step repository operations"""

    @abstractmethod
    async def create_step(self, tenant_id: UUID, project_id: UUID, route_id: UUID, step: Step) -> Step:
        """Create a new step"""
        pass

    @abstractmethod
    async def update_step(self, id: str, step: Step) -> Step:
        """Update an existing step"""
        pass

    @abstractmethod
    async def get_steps_by_route(
        self,
        tenant_id: UUID,
        project_id: UUID,
        route_id: UUID,
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "step_number",
        sort_order: int = 1,
    ) -> List[Step]:
        """Get steps by route with pagination"""
        pass

    @abstractmethod
    async def get_step_by_id(self, tenant_id: UUID, project_id: UUID, route_id: UUID, step_id: UUID) -> Step:
        """Get a step by ID"""
        pass
