from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from uuid import UUID

from app.core.models.project_note import ProjectNote
from app.core.models.project import NoteType


class ProjectNoteRepository(ABC):
    """Abstract base class for project note repositories"""

    @abstractmethod
    async def get_project_notes(
        self, tenant_id: UUID, project_id: UUID, sort_by: Optional[str] = None, note_type: Optional[str] = None
    ) -> List[ProjectNote]:
        """
        Retrieve all notes for a project.

        Args:
            tenant_id: The tenant ID
            project_id: The project ID
            sort_by: Sort by 'date', 'priority', or 'type'
            note_type: Filter by note type ('patent_claims', 'patent_reactions', 'pathways', 'quicknotes')
        """
        pass

    @abstractmethod
    async def create_project_note(
        self, tenant_id: UUID, project_id: UUID, user_id: UUID, note: ProjectNote
    ) -> ProjectNote:
        """
        Create a new note in the project.
        """
        pass

    @abstractmethod
    async def update_project_note(
        self, tenant_id: UUID, project_id: UUID, note_id: UUID, user_id: UUID, note: ProjectNote
    ) -> ProjectNote:
        """
        Update an existing note in the project.
        """
        pass

    @abstractmethod
    async def delete_project_note(self, tenant_id: UUID, project_id: UUID, user_id: UUID, note_id: UUID) -> None:
        """
        Delete a note from the project.
        """
        pass

    @abstractmethod
    async def get_quick_notes(
        self, tenant_id: UUID, project_id: UUID
    ) -> List[ProjectNote]:
        """
        Retrieve all quick notes for a project.
        """
        pass
    
    @abstractmethod
    async def get_shortlisted_claim_count(self, tenant_id: UUID, project_id: UUID, note_type: NoteType) -> int:
        """Returns the count for a specific note type"""
        pass
