from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any

from app.core.models.reaction import Reaction


class ReactionRepository(ABC):
    """Interface for reaction repository operations"""

    @abstractmethod
    async def create_reaction(self, reaction: Reaction) -> str:
        """Create a new reaction"""
        pass

    @abstractmethod
    async def update_reaction(self, id: str, reaction: Reaction) -> bool:
        """Update an existing reaction"""
        pass

    @abstractmethod
    async def get_reactions_by_step(
        self, step_id: str, skip: int = 0, limit: int = 100, sort_by: str = "created_at", sort_order: int = -1
    ) -> List[Reaction]:
        """Get reactions by step with pagination"""
        pass

    @abstractmethod
    async def get_reactions_by_status(self, status: str, skip: int = 0, limit: int = 100) -> List[Reaction]:
        """Get reactions by status"""
        pass

    @abstractmethod
    async def get_reactions_by_tags(self, tags: List[str], skip: int = 0, limit: int = 100) -> List[Reaction]:
        """Get reactions by tags"""
        pass

    @abstractmethod
    async def search_reactions(self, query: str, skip: int = 0, limit: int = 100) -> List[Reaction]:
        """Search reactions by name or description"""
        pass

    @abstractmethod
    async def get_reaction_stats(self) -> Dict[str, Any]:
        """Get reaction statistics"""
        pass
