from abc import ABC, abstractmethod
from typing import Optional, List, Dict
from uuid import UUID

from app.core.models.request_log import RequestLog, RequestType, RequestStatus


class RequestLogRepository(ABC):
    @abstractmethod
    async def create_log(self, log: RequestLog) -> RequestLog:
        pass

    @abstractmethod
    async def update_status(self, id: UUID, status: RequestStatus, error_message: str) -> bool:
        pass

    @abstractmethod
    async def get_request_ids_by_project_id(
        self, tenant_id: UUID, project_id: UUID, request_type: RequestType, include_statuses: List[RequestStatus]
    ) -> List[UUID]:
        """Get request IDs for a project and request type where status is in include_statuses"""
        pass

    @abstractmethod
    async def get_all_logs_by_project_id(self, project_id: UUID) -> List[RequestLog]:
        pass

    @abstractmethod
    async def get_project_info_from_request_id(self, request_id: UUID) -> RequestLog:
        pass
    @abstractmethod
    async def get_project_request_status(self, tenant_id: UUID, request_types: List[RequestType], project_ids: Optional[List[UUID]]) -> Dict:
        pass
