from abc import ABC, abstractmethod
from typing import List, Optional
from uuid import UUID

from app.core.models.project import Project
from app.core.models.paginated_response import PaginatedResponse


class ProjectRepository(ABC):
    """Interface for project repository operations"""

    @abstractmethod
    async def create_project(self, tenant_id: UUID, project: Project) -> Project:
        """Create a new project and return its ID"""
        pass

    @abstractmethod
    async def update_project(self, tenant_id: UUID, id: UUID, project: dict) -> Project:
        """Update an existing project and return success status"""
        pass

    @abstractmethod
    async def get_project(self, tenant_id: UUID, id: UUID) -> Optional[Project]:
        """Get a project by ID"""
        pass

    @abstractmethod
    async def get_projects_by_tenant(self, tenant_id: UUID, additional_filters: Optional[dict] = None, skip: int = 0, limit: int = 100) -> PaginatedResponse[Project]:
        """Get projects by tenant with pagination"""
        pass

    @abstractmethod
    async def search_projects(self, tenant_id: UUID, query: str, skip: int = 0, limit: int = 100) -> PaginatedResponse[Project]:
        """Search projects by name or compound name"""
        pass

    @abstractmethod
    async def delete_project(self, tenant_id: UUID, id: UUID) -> bool:
        """Delete a project by ID"""
        pass
