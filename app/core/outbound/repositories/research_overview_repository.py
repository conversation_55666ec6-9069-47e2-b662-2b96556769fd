from abc import ABC, abstractmethod
from datetime import datetime
from uuid import UUID
from app.core.models.project import Project


class ResearchOverviewRepository(ABC):
    """Repository interface for Research Overview"""

    @abstractmethod
    async def get_weekly_patent_count(self, tenant_id: UUID, start_date: datetime, end_date: datetime) -> int:
        pass

    @abstractmethod
    async def get_weekly_pathways_count(self, tenant_id: UUID, start_date: datetime, end_date: datetime) -> int:
        pass

    @abstractmethod
    async def get_total_projects(self, tenant_id: UUID) -> int:
        pass

    @abstractmethod
    async def get_ongoing_projects(self, tenant_id: UUID) -> int:
        pass

    @abstractmethod
    async def get_top_pathway_contributor_raw(self, tenant_id: UUID, last_24: datetime) -> dict:
        pass

    @abstractmethod
    async def get_top_patent_contributor_raw(self, tenant_id: UUID, last_24: datetime) -> dict:
        pass

    @abstractmethod
    async def get_project_by_id(self, project_id: str, tenant_id: UUID) -> Project:
        pass
