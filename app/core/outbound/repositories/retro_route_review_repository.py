from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from uuid import UUID
from app.core.models.retro_route_review import RetroRouteReview


class RetroRouteReviewRepository(ABC):
    """Interface for route Status repository operations"""

    @abstractmethod
    async def create(self, tenant_id: UUID, retro_route_review: RetroRouteReview) -> RetroRouteReview:
        """
        Create a new retro route review
        """
        pass

    @abstractmethod
    async def update(self, tenant_id: UUID, id: UUID, retro_route_review: RetroRouteReview) -> RetroRouteReview:
        """
        Update a retro route review
        """
        pass

    @abstractmethod
    async def get_by_project_id(self, tenant_id: UUID, project_id: UUID) -> List[RetroRouteReview]:
        """
        Get all retro route reviews for a project
        """
        pass

    @abstractmethod
    async def get_by_retro_route_id(self, tenant_id: UUID, project_id: UUID, retro_route_id: UUID) -> RetroRouteReview:
        """
        Get a retro route review by retro route id
        """
        pass

    @abstractmethod
    async def get_by_retro_route_ids(
        self, tenant_id: UUID, project_id: UUID, retro_route_ids: List[UUID]
    ) -> List[RetroRouteReview]:
        """
        Get a retro route review by retro route ids
        """
        pass

    @abstractmethod
    async def get_retro_route_ids_by_status(
        self, tenant_id: UUID, project_id: UUID, status: str
    ) -> List[UUID]:
        """
        Get retro route IDs that have a specific status
        """
        pass
