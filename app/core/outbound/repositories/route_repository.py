from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from uuid import UUID

from app.core.models.route import Route


class RouteRepository(ABC):
    """Interface for route repository operations"""

    @abstractmethod
    async def create_route(self, tenant_id: UUID, project_id: UUID, route: Route) -> Route:
        """Create a new route"""
        pass

    @abstractmethod
    async def update_route(self, tenant_id: UUID, project_id: UUID, id: UUID, route: Dict[str, Any]) -> Route:
        """Update an existing route"""
        pass

    @abstractmethod
    async def get_route(self, tenant_id: UUID, project_id: UUID, id: UUID) -> Optional[Route]:
        """Get a route by id"""
        pass

    @abstractmethod
    async def get_routes_by_project(
        self,
        tenant_id: UUID,
        project_id: UUID,
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "created_at",
        sort_order: int = -1,
    ) -> List[Route]:
        """Get routes by project with pagination"""
        pass

    @abstractmethod
    async def search_routes(
        self, tenant_id: UUID, project_id: UUID, query: str, skip: int = 0, limit: int = 100
    ) -> List[Route]:
        """Search routes by name or description"""
        pass
