from uuid import UUID
from app.core.models.retro_route import Re<PERSON><PERSON>out<PERSON>
from typing import Optional, List, Dict, Any
from abc import ABC, abstractmethod
from app.core.models.paginated_response import PaginatedResponse
from app.core.models.retro_route import RetroRouteFilters


class RetroRunnerService(ABC):
    def __init__(self):
        pass

    @abstractmethod
    async def trigger_retrosynthesis(self, payload: Dict[str, Any]):
        pass

    @abstractmethod
    async def fetch_routes_by_request_id(
        self,
        tenant_id: UUID,
        project_id: UUID,
        request_ids: List[UUID],
        raw_reactants: Optional[List[str]] = None,
        reactions: Optional[List[str]] = None,
        reagents: Optional[List[str]] = None,
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "created_at",
        sort_order: int = -1,
    ) -> PaginatedResponse[RetroRoute]:
        pass

    @abstractmethod
    async def fetch_by_request_id_and_retro_route_id(
        self, request_ids: List[UUID], retro_route_id: UUID
    ) -> Optional[RetroRoute]:
        pass

    @abstractmethod
    async def fetch_retro_route_filters(self, request_ids: List[UUID]) -> RetroRouteFilters:
        pass
