from abc import ABC, abstractmethod
from uuid import UUID
from typing import List, Dict, Any, Tuple
from app.core.models.patent import Patent, PatentFilter, PatentSortBy, SortOrder
from app.core.models.paginated_response import PaginatedResponse


class AIPatentProcessor(ABC):

    def __init__(self):
        pass

    @abstractmethod
    async def trigger_patent_processing(self, payload: Dict[str, Any]):
        pass

    @abstractmethod
    async def get_patents_by_request_ids(
        self,
        request_ids: List[UUID],
        sort_by: PatentSortBy,
        sort_order: SortOrder,
        reaction_types: List[str],
        claim_types: List[str],
        filing_companies: List[str],
        patent_offices: List[str],
        skip: int,
        limit: int,
    ) -> PaginatedResponse[Patent]:
        pass

    @abstractmethod
    async def get_patent_by_id(self, patent_id: UUID) -> Patent:
        pass

    @abstractmethod
    async def get_patent_filter_data(self, request_ids: List[UUID]) -> PatentFilter:
        pass

    @abstractmethod
    async def get_patent_count_by_request_ids(
        self,
        request_ids: List[UUID]
    ) -> int:
        pass
