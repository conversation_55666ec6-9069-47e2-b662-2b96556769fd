from typing import List, <PERSON><PERSON>
from uuid import UUID, uuid4

from app.common.utils import get_current_date_time
from app.core.outbound.repositories.request_log_repository import RequestLogRepository
from app.core.outbound.literature_service.ai_patent_processor import AIPatentProcessor
from app.core.models.patent import Patent, PatentTaskData, PatentInputType, PatentFilter, PatentSortBy, SortOrder
from app.core.models.request_log import RequestType, RequestStatus, RequestLog
from app.core.models.paginated_response import PaginatedResponse


class PatentService:
    """Service for handling patent-related business logic"""

    def __init__(
        self,
        request_log_repository: RequestLogRepository,
        ai_patent_processor: AIPatentProcessor,
    ):
        self.request_log_repository = request_log_repository
        self.ai_patent_processor = ai_patent_processor

    async def list_patents(
        self,
        tenant_id: UUID,
        project_id: UUID,
        sort_by: PatentSortBy,
        sort_order: SortOrder,
        reaction_types: List[str],
        claim_types: List[str],
        filing_companies: List[str],
        patent_offices: List[str],
        skip: int,
        limit: int,
    ) -> <PERSON><PERSON>[PaginatedResponse[Patent], RequestStatus]:
        """Get patents by tenant and project"""
        request_ids = await self.request_log_repository.get_request_ids_by_project_id(
            tenant_id, project_id, RequestType.PATENT, [RequestStatus.QUEUED, RequestStatus.RUNNING, RequestStatus.SUCCESS]
        )

        if not request_ids:
            raise Exception("No success or in progress patent request found")

        paginated_patents = await self.ai_patent_processor.get_patents_by_request_ids(
            request_ids, sort_by, sort_order, reaction_types, claim_types, filing_companies, patent_offices, skip, limit
        )

        request_data = await self.request_log_repository.get_project_request_status(tenant_id, [RequestType.PATENT], [project_id])
        patent_generation_status = request_data.get(str(project_id), {}).get(RequestType.PATENT, RequestStatus.QUEUED)
        return paginated_patents, patent_generation_status


    async def get_patent(self, patent_id: UUID) -> Patent:
        """Get a patent by ID"""
        return await self.ai_patent_processor.get_patent_by_id(patent_id)

    async def get_patent_filters(self, tenant_id: UUID, project_id: UUID) -> PatentFilter:
        """Get filter data for patent list"""

        request_ids = await self.request_log_repository.get_request_ids_by_project_id(
            tenant_id, project_id, RequestType.PATENT, [RequestStatus.QUEUED, RequestStatus.RUNNING, RequestStatus.SUCCESS]
        )

        if not request_ids:
            raise Exception("No success or in progress patent request found")

        return await self.ai_patent_processor.get_patent_filter_data(request_ids)

    async def trigger_patent_processing(self, tenant_id: UUID, project_id: UUID, user_id: UUID, patent_task_data: PatentTaskData):
        """Trigger patent processing"""

        patent_number = patent_task_data.input_string.replace(" ", "")
        if patent_task_data.input_type == PatentInputType.URL:
            patent_number = patent_number.rstrip('/').split("/")[-1].replace(".pdf", "")

        if not patent_number:
            raise Exception("Invalid patent URL")

        patent_request_id = uuid4()
        patent_task_payload = {
            "request_id": patent_request_id,
            "tenant_id": tenant_id,
            "user_id": user_id,
            "patent_id": patent_number,
            "patent_task_data": patent_task_data.model_dump()
        }
        patent_processing_result = await self.ai_patent_processor.trigger_patent_processing(patent_task_payload)

        if not patent_processing_result:
            raise Exception("Patent task failed to insert into the queue")

        created_at = get_current_date_time()
        await self.request_log_repository.create_log(
            RequestLog(
                id=patent_request_id,
                tenant_id=tenant_id,
                project_id=project_id,
                user_id=user_id,
                request_type=RequestType.PATENT,
                status=RequestStatus.QUEUED,
                created_at=created_at,
                updated_at=created_at,
            )
        )
