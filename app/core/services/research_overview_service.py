from datetime import datetime, timedelta
from uuid import UUID
from app.core.models.research_overview import ResearchOverview, WeeklyStatus, ResearchStatus, TopContributor
from app.core.outbound.repositories.research_overview_repository import ResearchOverviewRepository


class ResearchOverviewService:
    def __init__(self, research_overview_repository: ResearchOverviewRepository = None):
        self.research_overview_repository = research_overview_repository

    async def get_overview(self, tenant_id: UUID) -> ResearchOverview:
        weekly_status = await self._build_weekly_status(tenant_id)
        research_status = await self._build_research_status(tenant_id)
        top_contributors = await self._build_top_contributors(tenant_id)

        return ResearchOverview(
            weekly_status=weekly_status,
            research_status=research_status,
            top_contributors=top_contributors
        )

    async def _build_weekly_status(self, tenant_id: UUID) -> list[WeeklyStatus]:
        today = datetime.utcnow()
        status = []

        for week_num in range(4, 0, -1):
            week_end_date = today - timedelta(days=(4 - week_num) * 7)
            week_start_date = week_end_date - timedelta(days=6)

            patent_count = await self.research_overview_repository.get_weekly_patent_count(tenant_id, week_start_date, week_end_date)
            pathways_count = await self.research_overview_repository.get_weekly_pathways_count(tenant_id, week_start_date, week_end_date)

            status.append(WeeklyStatus(
                week=str(week_num),
                week_end_date=week_end_date.strftime("%Y-%m-%d"),
                no_of_patent_notes=patent_count,
                no_of_ai_pathway_shortlisted=pathways_count
            ))

        return status

    async def _build_research_status(self, tenant_id: UUID) -> ResearchStatus:
        total = await self.research_overview_repository.get_total_projects(tenant_id)
        ongoing = await self.research_overview_repository.get_ongoing_projects(tenant_id)
        return ResearchStatus(
            total_projects=total,
            in_progress_projects=ongoing,
            completed_projects=0
        )

    async def _build_top_contributors(self, tenant_id: UUID) -> list[TopContributor]:
        contributors = []
        last_24 = datetime.utcnow() - timedelta(hours=24)

        record = await self.research_overview_repository.get_top_pathway_contributor_raw(tenant_id, last_24)
        if record:
            contributor = await self._process_top_contributor(record, tenant_id, "pathways")
            if contributor:
                contributors.append(contributor)

        record = await self.research_overview_repository.get_top_patent_contributor_raw(tenant_id, last_24)
        if record:
            project_id = record["_id"]["project_id"]
            project = await self.research_overview_repository.get_project_by_id(project_id, tenant_id)
            project_name = getattr(project, "compound_name")
            count = record.get("count", 0)
            summary = f"{count} Patents claimed in last 24 hours"
            contributor = TopContributor(
                researcher_name=record["_id"].get("user_name"),
                project_name=project_name,
                summary=summary
            )
            contributors.append(contributor)
        return contributors

    async def _process_top_contributor(self, record: dict, tenant_id: UUID, note_type: str) -> TopContributor:
        project_id = record["_id"]["project_id"]
        project = await self.research_overview_repository.get_project_by_id(project_id, tenant_id)
        project_name = getattr(project, "compound_name")
        collaborators = getattr(project, "collaborators")

        if note_type == "pathways":
            user_id = record["_id"].get("created_by")
            researcher_name = collaborators.get(user_id, {}).get("user_name", "Unknown") if user_id else "Unknown"
            summary = f"{record.get('count', 0)} Pathways shortlisted in last 24 hours"
        else:  # patent_claims
            researcher_name = record["_id"].get("user_name") or record["_id"].get("created_by", "Unknown")
            summary = f"{record.get('count', 0)} Patents claimed in last 24 hours"

        return TopContributor(
            researcher_name=researcher_name,
            project_name=project_name,
            summary=summary
        )
