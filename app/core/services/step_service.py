from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from app.core.models.step import Step
from app.core.outbound.repositories.step_repository import StepRepository


class StepService:
    """Service for handling step-related business logic"""

    def __init__(self, step_repository: StepRepository):
        self.step_repository = step_repository

    async def create_step(self, tenant_id: UUID, project_id: UUID, route_id: UUID, step: Step) -> Step:
        """Create a new step"""
        return await self.step_repository.create_step(tenant_id, project_id, route_id, step)

    async def update_step(self, id: str, step: Step) -> bool:
        """Update an existing step"""
        return await self.step_repository.update_step(id, step)

    async def get_steps_by_route(
        self,
        tenant_id: UUID,
        project_id: UUID,
        route_id: UUID,
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "step_number",
        sort_order: int = 1,
    ) -> List[Step]:
        """Get steps by route with pagination"""
        return await self.step_repository.get_steps_by_route(
            tenant_id, project_id, route_id, skip, limit, sort_by, sort_order
        )

    async def get_step_by_id(self, tenant_id: UUID, project_id: UUID, route_id: UUID, step_id: UUID) -> Step:
        """Get a step by ID"""
        return await self.step_repository.get_step_by_id(tenant_id, project_id, route_id, step_id)
