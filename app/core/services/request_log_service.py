from uuid import UUID

from loguru import logger

from app.core.models.patent import PatentTag
from app.core.models.request_log import RequestStatus, RequestType
from app.core.outbound.repositories.request_log_repository import RequestLogRepository
from app.core.outbound.repositories.project_repository import ProjectRepository
from app.core.outbound.literature_service.ai_patent_processor import AIPatentProcessor


class RequestLogService:
    def __init__(self, request_log_repository: RequestLogRepository, project_repository: ProjectRepository, ai_patent_processor: AIPatentProcessor):
        self.request_log_repository = request_log_repository
        self.project_repository = project_repository
        self.ai_patent_processor = ai_patent_processor

    async def process_request_queue_status(self, request_type: RequestType, payload: dict):
        request_id = payload.get("request_id")
        status = payload.get("status", "").upper()

        logger.info(f"[RESULT RECEIVED] request_id={request_id}, status={status}")

        if not request_id or status not in RequestStatus:
            logger.error(f"Invalid result payload: {payload}")
            return False

        update_result = await self.request_log_repository.update_status(
            UUID(request_id), RequestStatus[status].value, payload.get("error_message", "")
        )
        logger.info(
            f"[REQUEST LOG UPDATE] request_id={request_id}, status={RequestStatus[status].value}, result={update_result}"
        )

        request_log = await self.request_log_repository.get_project_info_from_request_id(request_id)
        if request_log is None:
            logger.error(f"[REQUEST LOG NOT FOUND] request_id={request_id}")
            raise Exception('Request log not found')

        tenant_id, project_id = request_log.tenant_id, request_log.project_id
        project = await self.project_repository.get_project(tenant_id, project_id)
        if project is None:
            logger.error(f"[PROJECT NOT FOUND] tenant_id={tenant_id}, project_id={project_id}")
            raise Exception('Project not found')
        
        logger.info(f"[GET PROJECT] project_id={project_id}")

        if request_type == RequestType.PATENT:
            request_ids = await self.request_log_repository.get_request_ids_by_project_id(
                tenant_id, project_id, RequestType.PATENT, [RequestStatus.SUCCESS]
            )
            # Get total patent count directly from Azure
            patent_count = await self.ai_patent_processor.get_patent_count_by_request_ids(request_ids)

            tag = None
            if patent_count > 50:
                tag = PatentTag.WELL_STUDIED.value
            elif 11 <= patent_count <= 50:
                tag = PatentTag.LITERATURE_RICH.value
            elif 5 < patent_count <= 10:
                tag = PatentTag.EMERGING_RESEARCH.value
            elif 0 < patent_count <= 5:
                tag = PatentTag.UNDER_RESEARCHED.value

            # Remove any of the four tags if present
            tag_options = [t.value for t in PatentTag]
            project.tags = [t for t in (project.tags or []) if t not in tag_options]
            if tag:
                project.tags.append(tag)

        # convert to dict
        project_dict = project.model_dump()
        for key in project.get_keys_not_allowed_to_update():
            project_dict.pop(key, None)
        updated_project = await self.project_repository.update_project(tenant_id, project_id, project_dict)

        if not updated_project:
            logger.error(f"[PROJECT UPDATE ERROR] tenant_id={tenant_id}, project_id={project_id}")
            raise Exception('Project update error')

        logger.info(f"[UPDATE PROJECT] project_id={project_id}")

        return update_result
