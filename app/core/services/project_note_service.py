from typing import List, Optional
from uuid import UUID, uuid4
from app.api.schemas.project_note import NoteType
from app.core.models.project_note import ProjectNote, ProjectNoteStats

from app.core.outbound.repositories.project_note_repository import ProjectNoteRepository


class ProjectNoteService:
    """Service for handling project notes-related business logic"""

    def __init__(self, project_note_repository: ProjectNoteRepository):
        self.project_note_repository = project_note_repository

    async def get_project_notes(
        self, tenant_id: UUID, project_id: UUID, sort_by: Optional[str] = None, note_type: Optional[str] = None
    ) -> List[ProjectNote]:
        """
        Retrieve all notes for a research workspace.
        """
        notes = await self.project_note_repository.get_project_notes(tenant_id, project_id, sort_by, note_type)
        if notes is None:
            return []
        return notes

    async def create_project_note(
        self, tenant_id: UUID, project_id: UUID, user_id: UUID, note: ProjectNote
    ) -> ProjectNote:
        """
        Create a new note in the research workspace.
        """
        if note.note_type == NoteType.QUICK_NOTES:
            data = await self.project_note_repository.get_quick_notes(tenant_id, project_id)
            count = len(data)
            note.name = f"Quick Note {count + 1}"  # You can modify directly since it's a model

        note.id = uuid4()
        created_note = await self.project_note_repository.create_project_note(
            tenant_id, project_id, user_id, note  # ← Pass the model, not a dict
        )
        return created_note

    async def update_project_note(
        self, tenant_id: UUID, project_id: UUID, note_id: UUID, user_id: UUID, note: ProjectNote
    ) -> ProjectNote:
        """
        Update an existing note in the research workspace.
        """
        # Here you would implement the logic to update a note
        # For now, we will just return a placeholder
        updated_note = await self.project_note_repository.update_project_note(
            tenant_id, project_id, note_id, user_id, note
        )
        return updated_note

    async def delete_project_note(self, tenant_id: UUID, project_id: UUID, user_id: UUID, note_id: UUID) -> None:
        """
        Delete a note from the project.
        """
        # Here you would implement the logic to delete a note
        # For now, we will just return None
        await self.project_note_repository.delete_project_note(tenant_id, project_id, user_id, note_id)

    @staticmethod
    def _compute_note_stats(notes: list) -> ProjectNoteStats:
        return ProjectNoteStats(
            patent_claims=sum(1 for n in notes if n.get("note_type") == "patent_claims"),
            patent_reactions=sum(1 for n in notes if n.get("note_type") == "patent_reactions"),
            pathways=sum(1 for n in notes if n.get("note_type") == "pathways"),
            quick_notes=sum(1 for n in notes if n.get("note_type") == "quick_notes"),
            high_priority=sum(1 for n in notes if n.get("priority") == "high"),
            total_notes=len(notes),
        )
