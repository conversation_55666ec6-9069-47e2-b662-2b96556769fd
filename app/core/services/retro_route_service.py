from typing import List, Optional, Tuple
from uuid import UUID, uuid4
from datetime import datetime, UTC

from loguru import logger

from app.core.models.paginated_response import PaginatedResponse
from app.core.models.request_log import RequestStatus, RequestType
from app.core.models.retro_route import RetroRoute, RetroRouteFilters, ReactionConfidence
from app.core.models.retro_route_review import RetroRouteReview, RetroRouteReviewStatus
from app.core.outbound.repositories.project_repository import ProjectRepository
from app.core.outbound.repositories.request_log_repository import RequestLogRepository
from app.core.outbound.repositories.retro_route_review_repository import RetroRouteReviewRepository
from app.core.outbound.retrorunner.retro_runner_service import RetroRunnerService
from app.common.utils import get_current_date_time


class RetroRouteService:
    def __init__(
        self,
        retro_runner_service: RetroRunnerService,
        request_log_repository: RequestLogRepository,
        retro_route_review_repository: RetroRouteReviewRepository,
        project_repository: ProjectRepository,
    ):
        self.retro_runner_service = retro_runner_service
        self.request_log_repository = request_log_repository
        self.retro_route_review_repository = retro_route_review_repository
        self.project_repository = project_repository

    async def save_retro_route_review(
        self,
        tenant_id: UUID,
        project_id: UUID,
        retro_route_id: UUID,
        user_id: UUID,
        review_text: str,
        review_status: RetroRouteReviewStatus,
    ) -> RetroRouteReview:
        """Save a review for a retro route"""
        # check if the review already exists
        existing_review = await self.retro_route_review_repository.get_by_retro_route_id(
            tenant_id=tenant_id, project_id=project_id, retro_route_id=retro_route_id
        )
        
        current_status = RetroRouteReviewStatus.PLANNED  # default status
        if existing_review:
            current_status = existing_review.status
        
        new_status = review_status
        
        if not existing_review:
            # if the review does not exist, create a new one
            review_data = RetroRouteReview(
                id=uuid4(),
                tenant_id=tenant_id,
                project_id=project_id,
                retro_route_id=retro_route_id,
                created_by=user_id,
                review_text=review_text,
                status=review_status,
                created_at=get_current_date_time(),
                updated_at=get_current_date_time(),
            )
            review = await self.retro_route_review_repository.create(tenant_id, review_data)
        else:
            # if the review exists, update the review
            existing_review.review_text = review_text
            existing_review.status = review_status
            existing_review.updated_by = user_id
            existing_review.updated_at = get_current_date_time()
            review = await self.retro_route_review_repository.update(tenant_id, existing_review.id, existing_review)

        # Update project's shortlisted_routes_count if status changed
        if new_status != current_status:
            try:
                project_info = await self.project_repository.get_project(tenant_id, project_id)

                if project_info:
                    # Calculate the change in shortlisted count
                    if current_status == RetroRouteReviewStatus.SHORTLISTED and new_status != RetroRouteReviewStatus.SHORTLISTED:
                        # Removing from shortlist
                        project_info.shortlisted_routes_count = max(0, project_info.shortlisted_routes_count - 1)
                    elif new_status == RetroRouteReviewStatus.SHORTLISTED and current_status != RetroRouteReviewStatus.SHORTLISTED:
                        # Adding to shortlist
                        project_info.shortlisted_routes_count += 1

                    # convert to dict
                    project_dict = project_info.model_dump()
                    # Remove fields that shouldn't be updated
                    for key in project_info.get_keys_not_allowed_to_update():
                        project_dict.pop(key, None)
                    
                    await self.project_repository.update_project(tenant_id, project_id, project_dict)
            except Exception as e:
                logger.error(f"Failed to update project shortlisted_routes_count: {e}")
                # Don't fail the review save operation if project count update fails
        
        return review

    async def get_retro_reviews_by_project(self, tenant_id: UUID, project_id: UUID) -> List[RetroRouteReview]:
        """Get all reviews for a project"""
        return await self.retro_route_review_repository.get_by_project_id(tenant_id, project_id)

    async def get_alternative_routes(self, tenant_id: UUID, project_id: UUID, retro_route_id: UUID):
        """Get alternative routes for a retro route"""
        # get the retro route
        retro_route = await self.get_retro_route_by_id(tenant_id, project_id, retro_route_id)
        if not retro_route:
            return None

        project_request_ids = await self._get_project_request_ids(tenant_id, project_id)
        if not project_request_ids:
            return None

        # Get all other routes for the project
        all_routes = await self.retro_runner_service.fetch_routes_by_request_id(
            tenant_id, project_id, project_request_ids
        )
        all_routes = [r for r in all_routes.data if r.id != retro_route_id]  # remove the retro route from the list
        if not all_routes:
            return None

        # get the metrics for the retro route
        retro_route_metrics = self._get_route_metrics(retro_route)

        # get the metrics for the alternative routes
        for r in all_routes:
            if hasattr(r, "model_dump"):
                r_dict = r.model_dump()
            elif hasattr(r, "dict"):
                r_dict = r.dict()
            else:
                r_dict = dict(r)
            if hasattr(r, "reagents"):
                if r.reagents is None:
                    r.reagents = []
            elif isinstance(r, dict):
                if r.get("reagents") is None:
                    r["reagents"] = []
            r.route_cost = self._calculate_route_cost(r_dict)

        route_num_steps = retro_route_metrics.get("num_steps")
        route_cost = retro_route_metrics.get("route_cost")
        route_score = retro_route_metrics.get("total_route_score")

        fewer_steps_routes = [r for r in all_routes if self._safe_number(r.num_steps, 0) < route_num_steps]
        lower_cost_routes = [r for r in all_routes if self._safe_number(r.route_cost, float("inf")) < route_cost]
        higher_score_routes = [
            r for r in all_routes if self._safe_number(r.total_route_score, float("-inf")) > route_score
        ]

        alternative_routes_response = {
            "fewer_steps_paths": {
                "count": len(fewer_steps_routes),
                "routes": fewer_steps_routes,
            },
            "lower_cost_paths": {
                "count": len(lower_cost_routes),
                "routes": lower_cost_routes,
            },
            "higher_score_paths": {
                "count": len(higher_score_routes),
                "routes": higher_score_routes,
            },
        }
        return alternative_routes_response

    async def get_retro_route_by_id(
        self, tenant_id: UUID, project_id: UUID, retro_route_id: UUID
    ) -> Optional[RetroRoute]:
        """Get a route by its retro_route_id"""
        project_request_ids = await self._get_project_request_ids(tenant_id, project_id)
        if not project_request_ids:
            return None

        return await self.retro_runner_service.fetch_by_request_id_and_retro_route_id(
            project_request_ids, retro_route_id
        )

    async def get_retro_route_filters(self, tenant_id: UUID, project_id: UUID) -> RetroRouteFilters:
        project_request_ids = await self._get_project_request_ids(tenant_id, project_id)
        if not project_request_ids:
            return None
        return await self.retro_runner_service.fetch_retro_route_filters(project_request_ids)

    async def get_retro_routes(
        self,
        tenant_id: UUID,
        project_id: UUID,
        raw_reactants: Optional[List[str]] = None,
        status: Optional[str] = None,
        reactions: Optional[List[str]] = None,
        reagents: Optional[List[str]] = None,
        sort_by: str = "created_at",
        sort_order: int = -1,
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[PaginatedResponse[RetroRoute], RequestStatus]:
        """Get routes with status and filters"""

        # Get all non-failed request id for the project for retro
        request_ids: List[UUID] = await self.request_log_repository.get_request_ids_by_project_id(
            tenant_id, project_id, RequestType.RETRO, [RequestStatus.QUEUED, RequestStatus.RUNNING, RequestStatus.SUCCESS]
        )

        retro_route_ids = None
        if status:
            retro_route_ids = await self.retro_route_review_repository.get_retro_route_ids_by_status(
                tenant_id, project_id, status
            )
            if not retro_route_ids:
                # If no routes match the status, return empty response
                return PaginatedResponse(data=[], total=0, skip=skip, limit=limit), RequestStatus.SUCCESS
         
        retro_routes = await self.retro_runner_service.fetch_routes_by_request_id(
                tenant_id, project_id, request_ids, raw_reactants, reactions, reagents, skip, limit, sort_by, sort_order, retro_route_ids=retro_route_ids
            )
            
        # Collect all route IDs as strings
        route_ids = [str(route.id) for route in retro_routes.data]

        # Fetch status for all routes
        all_reviews = await self.retro_route_review_repository.get_by_retro_route_ids(tenant_id, project_id, route_ids)

        all_reviews_status_map = {str(review.retro_route_id): review.status for review in all_reviews}
        all_reviews_text_map = {str(review.retro_route_id): review.review_text for review in all_reviews}

        # Build response routes with merged status and calculated costs
        routes: List[RetroRoute] = []
        for route in retro_routes.data:
            route_dict = route.model_dump() if hasattr(route, "model_dump") else dict(route)
            route_id = str(route_dict.get("id") or route_dict.get("_id"))
            route_dict["status"] = all_reviews_status_map.get(route_id, RetroRouteReviewStatus.PLANNED)
            route_dict["tags"] = self._extract_tags(route)
            route_dict["reagents"] = self._extract_reagents(route)
            route_dict["reactions"] = self._extract_reaction(route)
            route_dict["route_cost"] = self._calculate_route_cost(route)
            route_dict["review_text"] = all_reviews_text_map.get(route_id)
            routes.append(RetroRoute.model_validate(route_dict))
        status_order = {RetroRouteReviewStatus.SHORTLISTED: 0, RetroRouteReviewStatus.PLANNED: 1, RetroRouteReviewStatus.REJECTED: 2}
        routes.sort(key=lambda r: status_order.get(r.status, 99))

        # For cost sorting, we need to get all routes first to calculate costs, then sort
        if sort_by == "cost":
            
            # Sort by route_cost
            routes.sort(key=lambda r: r.route_cost or 0, reverse=(sort_order == -1))
            
            # Apply pagination after sorting
            routes = routes[skip:skip + limit]

        request_data = await self.request_log_repository.get_project_request_status(tenant_id, [RequestType.RETRO], [project_id])
        retro_route_generation_status = request_data.get(str(project_id), {}).get(RequestType.RETRO, RequestStatus.QUEUED)

        return PaginatedResponse(data=routes, total=retro_routes.total, skip=skip, limit=limit), retro_route_generation_status

    async def get_route_details_by_id(self, tenant_id: UUID, project_id: UUID, retro_route_id: UUID):
        core_route = await self.get_retro_route_by_id(tenant_id, project_id, retro_route_id)
        if not core_route:
            return None

        # Convert to dict
        if hasattr(core_route, "model_dump"):
            route_dict = core_route.model_dump()
        elif hasattr(core_route, "dict"):
            route_dict = core_route.dict()
        else:
            route_dict = dict(core_route)

        # Add status
        status_doc = await self.retro_route_review_repository.get_by_retro_route_id(tenant_id, project_id, retro_route_id)
        route_dict["status"] = status_doc.status if status_doc else RetroRouteReviewStatus.PLANNED
        if "data" in route_dict:
            route_dict["tags"] = [
                step.get("rxn_class", {}).get("reaction_name")
                for step in route_dict["data"]
                if step.get("rxn_class", {}).get("reaction_name")
            ]
        else:
            route_dict["tags"] = []

        # Convert _id to id
        if "_id" in route_dict:
            route_dict["id"] = str(route_dict.pop("_id"))

        # Normalize the data structure for consistent frontend consumption
        normalized_route = self._normalize_route_data(route_dict)
        
        # Recalculate route cost as sum of all step costs after normalization
        total_route_cost = sum(step.get("step_cost", 0.0) for step in normalized_route.get("data", []))
        normalized_route["route_cost"] = total_route_cost
        # Keep total_cost as is, don't modify it
        
        return normalized_route

    def _normalize_route_data(self, route_dict: dict) -> dict:
        """
        Normalize route data to ensure consistent structure for frontend consumption.
        This handles data inconsistencies from different LLM responses and MongoDB storage.
        """
        logger.debug(f"Normalizing route data for route_id: {route_dict.get('id') or route_dict.get('_id')}")
        
        normalized = {}
        
        # Handle ID fields
        normalized["id"] = str(route_dict.get("id") or route_dict.get("_id", ""))
        normalized["unique_id"] = self._normalize_unique_id(route_dict.get("unique_id"))
        normalized["request_id"] = str(route_dict.get("request_id", ""))
        normalized["route_id"] = route_dict.get("route_id")
        
        # Handle date fields - ensure they are ISO strings
        normalized["created_at"] = self._normalize_date(route_dict.get("created_at"))
        normalized["updated_at"] = self._normalize_date(route_dict.get("updated_at"))
        
        # Handle basic fields with defaults
        normalized["target_smiles"] = route_dict.get("target_smiles", "")
        normalized["route_name"] = route_dict.get("route_name", "Unnamed Route")
        normalized["num_steps"] = route_dict.get("num_steps", 0)
        normalized["total_route_score"] = route_dict.get("total_route_score", 0.0)
        normalized["total_cost"] = route_dict.get("total_cost", 0.0)
        normalized["route_reaction_img"] = route_dict.get("route_reaction_img", "")
        normalized["raw_reactants"] = route_dict.get("raw_reactants", [])
        
        # Handle status and computed fields
        normalized["status"] = route_dict.get("status", "planned")
        normalized["route_cost"] = route_dict.get("route_cost", 0.0)
        normalized["tags"] = route_dict.get("tags", [])
        
        # Normalize the data array (steps)
        normalized["data"] = self._normalize_steps_data(route_dict.get("data", []))
        
        logger.debug(f"Route data normalization completed for route_id: {normalized['id']}")
        return normalized
    
    def _normalize_unique_id(self, unique_id) -> str:
        """Normalize unique_id field which can be a number or object with $numberLong"""
        if unique_id is None:
            return ""
        if isinstance(unique_id, dict) and "$numberLong" in unique_id:
            logger.debug(f"Normalizing unique_id from MongoDB format: {unique_id}")
            return str(unique_id["$numberLong"])
        return str(unique_id)
    
    def _normalize_date(self, date_value) -> datetime:
        """Normalize date fields to datetime objects"""
        if date_value is None:
            return datetime.now(UTC)  # Default to current UTC time
        if isinstance(date_value, dict) and "$date" in date_value:
            # MongoDB date object - convert string to datetime
            logger.debug(f"Normalizing date from MongoDB format: {date_value}")
            date_string = str(date_value["$date"])
            try:
                return datetime.fromisoformat(date_string.replace('Z', '+00:00'))
            except ValueError:
                # Fallback to parsing with different formats
                try:
                    return datetime.strptime(date_string, "%Y-%m-%dT%H:%M:%S.%fZ")
                except ValueError:
                    try:
                        return datetime.strptime(date_string, "%Y-%m-%dT%H:%M:%SZ")
                    except ValueError:
                        logger.warning(f"Could not parse date: {date_string}, using current time")
                        return datetime.now(UTC)
        if isinstance(date_value, str):
            try:
                return datetime.fromisoformat(date_value.replace('Z', '+00:00'))
            except ValueError:
                logger.warning(f"Could not parse date string: {date_value}, using current time")
                return datetime.now(UTC)
        if isinstance(date_value, datetime):
            return date_value
        # If it's any other type, try to convert to string first
        try:
            date_string = str(date_value)
            return datetime.fromisoformat(date_string.replace('Z', '+00:00'))
        except (ValueError, AttributeError):
            logger.warning(f"Could not convert date value: {date_value}, using current time")
            return datetime.now(UTC)
    
    def _normalize_steps_data(self, steps_data: list) -> list:
        """Normalize steps data to ensure consistent structure"""
        normalized_steps = []
        
        for step in steps_data:
            if not isinstance(step, dict):
                logger.warning(f"Skipping non-dict step data: {type(step)}")
                continue
                
            normalized_step = {
                "step": step.get("step", 0),
                "reaction_id": step.get("reaction_id", ""),
                "reaction_string": step.get("reaction_string", ""),
                "reaction_name": step.get("reaction_name", ""),
                "retro_smiles": step.get("retro_smiles", ""),
                "reagents": step.get("reagents", ""),
                "forward_prediction": step.get("forward_prediction", ""),
                "prob_forward_1": step.get("prob_forward_1", 0.0),
                "prob_forward_2": step.get("prob_forward_2", 0.0),
                "score": step.get("score", 0.0),
                "reaction_smiles_img": step.get("reaction_smiles_img", ""),
                "other_information": self._normalize_other_information(step.get("other_information")),
                "reactants": self._normalize_reactants(step.get("reactants", [])),
            }
            
            # Calculate step cost
            normalized_step["step_cost"] = self._calculate_step_cost(normalized_step["other_information"])
            
            # Handle rxn_class
            rxn_class = step.get("rxn_class", {})
            if isinstance(rxn_class, dict):
                normalized_step["rxn_class"] = {
                    "rank": rxn_class.get("rank", 0),
                    "reaction_num": rxn_class.get("reaction_num", ""),
                    "reaction_name": rxn_class.get("reaction_name", ""),
                    "reaction_classnum": rxn_class.get("reaction_classnum", ""),
                    "reaction_classname": rxn_class.get("reaction_classname", ""),
                    "reaction_superclassnum": rxn_class.get("reaction_superclassnum", ""),
                    "reaction_superclassname": rxn_class.get("reaction_superclassname", ""),
                    "prediction_certainty": rxn_class.get("prediction_certainty", 0.0),
                }
                # Add reaction_confidence based on prediction_certainty
                prediction_certainty = rxn_class.get("prediction_certainty", 0.0)
                if prediction_certainty < 0.33:
                    normalized_step["reaction_confidence"] = ReactionConfidence.LOW
                elif prediction_certainty < 0.66:
                    normalized_step["reaction_confidence"] = ReactionConfidence.MEDIUM
                else:
                    normalized_step["reaction_confidence"] = ReactionConfidence.HIGH
            else:
                logger.warning(f"rxn_class is not a dict in step: {type(rxn_class)}")
                normalized_step["rxn_class"] = {
                    "rank": 0,
                    "reaction_num": "",
                    "reaction_name": "",
                    "reaction_classnum": "",
                    "reaction_classname": "",
                    "reaction_superclassnum": "",
                    "reaction_superclassname": "",
                    "prediction_certainty": 0.0,
                }
            
            normalized_steps.append(normalized_step)
        
        return normalized_steps
    
    def _normalize_other_information(self, other_info) -> dict:
        """Normalize other_information field which can be a dict or error string"""
        if other_info is None:
            return {}
        
        if isinstance(other_info, str):
            # If it's an error message string, return it in a structured format
            return {
                "error_message": other_info,
                "reaction_smiles_interpreted": "",
                "reaction_details": {
                    "reactants_identified": [],
                    "products_identified": [],
                    "reaction_name": ""
                },
                "reagents_and_solvents": [],
                "reaction_conditions": {
                    "temperature_from_dataset": "",
                    "time_from_dataset": "",
                    "yield_from_dataset": "",
                    "atmosphere_llm_or_dataset": ""
                },
                "safety_and_notes": {
                    "safety": "",
                    "notes": ""
                },
                "procedure_steps": [],
                "experimental_data_source_note": "",
                "visualization_path": ""
            }
        
        if isinstance(other_info, dict):
            # Ensure all expected fields exist with defaults
            return {
                "reaction_smiles_interpreted": other_info.get("reaction_smiles_interpreted", ""),
                "reaction_details": {
                    "reactants_identified": other_info.get("reaction_details", {}).get("reactants_identified", []),
                    "products_identified": other_info.get("reaction_details", {}).get("products_identified", []),
                    "reaction_name": other_info.get("reaction_details", {}).get("reaction_name", "")
                },
                "reagents_and_solvents": other_info.get("reagents_and_solvents", []),
                "reaction_conditions": {
                    "temperature_from_dataset": other_info.get("reaction_conditions", {}).get("temperature_from_dataset", ""),
                    "time_from_dataset": other_info.get("reaction_conditions", {}).get("time_from_dataset", ""),
                    "yield_from_dataset": other_info.get("reaction_conditions", {}).get("yield_from_dataset", ""),
                    "atmosphere_llm_or_dataset": other_info.get("reaction_conditions", {}).get("atmosphere_llm_or_dataset", "")
                },
                "safety_and_notes": {
                    "safety": other_info.get("safety_and_notes", {}).get("safety", ""),
                    "notes": other_info.get("safety_and_notes", {}).get("notes", "")
                },
                "procedure_steps": other_info.get("procedure_steps", []),
                "experimental_data_source_note": other_info.get("experimental_data_source_note", ""),
                "visualization_path": other_info.get("visualization_path", "")
            }
        
        logger.warning(f"other_information is neither string nor dict: {type(other_info)}")
        return {}
    
    def _normalize_reactants(self, reactants: list) -> list:
        """Normalize reactants array to ensure consistent structure"""
        normalized_reactants = []
        
        for reactant in reactants:
            if isinstance(reactant, dict):
                normalized_reactant = {
                    "smiles": reactant.get("smiles", ""),
                    "name": reactant.get("name", ""),
                    "synthesis_score": reactant.get("synthesis_score", 0.0),
                    "is_terminal": reactant.get("is_terminal", "")
                }
                normalized_reactants.append(normalized_reactant)
            else:
                logger.warning(f"Skipping non-dict reactant: {type(reactant)}")
        
        return normalized_reactants

    def _extract_tags(self, route):
        tags = []
        data = getattr(route, "data", None) or []
        for step in data:
            try:
                other_info = step.get("other_information", {})
                if isinstance(other_info, dict):
                    reaction_details = other_info.get("reaction_details", {})
                    reaction_name = reaction_details.get("reaction_name")
                    if reaction_name:
                        tags.append(reaction_name)
                else:
                    # Log and skip if not a dict
                    logger.warning(f"Step other_information is not a dict: {other_info}")
            except Exception as e:
                logger.warning(f"Error extracting tags from step: {e}")
        return tags

    def _extract_reagents(self, route):
        reagents = set()  # Use a set to automatically handle duplicates
        data = getattr(route, "data", None) or []
        for step in data:
            try:
                other_info = step.get("other_information", {})
                if isinstance(other_info, dict):
                    reagent_details = other_info.get("reagents_and_solvents", [])
                    for reagent in reagent_details:
                        reagent_name = reagent.get("name")
                        if reagent_name:
                            reagents.add(reagent_name)  # Add to the set
                else:
                    logger.warning(f"Step other_information is not a dict: {other_info}")
            except Exception as e:
                logger.warning(f"Error extracting reagents from step: {e}")
        return list(reagents)  # Convert back to a list before returning

    def _extract_reaction(self, route):
        reactions = set()  # Use a set to automatically handle duplicates
        data = getattr(route, "data", None) or []
        for step in data:
            try:
                rxn_class = step.get("rxn_class", {})
                if isinstance(rxn_class, dict):
                    reaction_name = rxn_class.get("reaction_name")
                    if reaction_name:
                        reactions.add(reaction_name)  # Add to the set
                else:
                    logger.warning(f"Step rxn_class is not a dict: {rxn_class}")
            except Exception as e:
                logger.warning(f"Error extracting reagents from step: {e}")
        return list(reactions)  # Convert back to a list before returning


    def _calculate_step_cost(self, other_info):
        """Calculate the cost of a single step based on reagents and solvents"""
        step_cost = 0.0
        
        # Check if other_info is a dictionary and not an error message
        if isinstance(other_info, dict):
            for reagent in other_info.get("reagents_and_solvents", []):
                price = reagent.get("price_per_unit")
                if isinstance(price, (int, float)):
                    step_cost += price
        else:
            # Log warning if other_info is not a dictionary (e.g., error message)
            logger.warning(f"other_information is not a dictionary in step: {other_info}")
        
        return step_cost

    def _calculate_route_cost(self, route_doc):
        """Calculate the total route cost as the sum of all step costs"""
        # If it's a Pydantic model, convert to dict
        if hasattr(route_doc, "model_dump"):
            route_doc = route_doc.model_dump()
        
        total_cost = 0.0
        
        # Calculate route cost as sum of all step costs
        for step in route_doc.get("data", []):
            step_cost = self._calculate_step_cost(step.get("other_information", {}))
            total_cost += step_cost
        
        return total_cost

    async def _get_project_request_ids(self, tenant_id: UUID, project_id: UUID) -> List[UUID]:
        return await self.request_log_repository.get_request_ids_by_project_id(
            tenant_id, project_id, RequestType.RETRO, [RequestStatus.QUEUED, RequestStatus.RUNNING, RequestStatus.SUCCESS]
        )

    def _safe_number(self, number: int | float, default: int | float) -> int | float:
        return number if isinstance(number, (int, float)) and number is not None else default

    def _get_route_metrics(self, route: RetroRoute) -> dict[str, int | float]:
        """Get the metrics for a route"""

        return {
            "num_steps": self._safe_number(route.num_steps, 0),
            "route_cost": self._safe_number(route.route_cost, float("inf")),
            "total_route_score": self._safe_number(route.total_route_score, float("-inf")),
        }
