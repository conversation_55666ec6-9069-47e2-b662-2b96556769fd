from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from app.core.models.route import Route
from app.core.outbound.repositories.route_repository import RouteRepository
from app.common.utils import get_current_date_time
from app.common.request_context import get_request_context


class RouteService:
    """Service for handling route-related business logic"""

    def __init__(self, route_repository: RouteRepository):
        self.route_repository = route_repository

    async def create_route(self, tenant_id: UUID, project_id: UUID, route: Route) -> Route:
        """Create a new route"""
        return await self.route_repository.create_route(tenant_id, project_id, route)

    async def update_route(self, tenant_id: UUID, project_id: UUID, id: UUID, route: Route) -> Route:
        """Update an existing route"""
        tenant_id, user_id = get_request_context()
        # create a dict from route
        route_dict = route.model_dump()
        # Dont update the below keys
        for key in Route.get_keys_not_allowed_to_update():
            route_dict.pop(key, None)
        route_dict["updated_at"] = get_current_date_time()
        route_dict["updated_by"] = user_id
        return await self.route_repository.update_route(tenant_id, project_id, id, route_dict)

    async def get_route_by_id(self, tenant_id: UUID, project_id: UUID, id: UUID) -> Optional[Route]:
        """Get a route by id"""
        return await self.route_repository.get_route(tenant_id, project_id, id)

    async def get_routes_by_project(
        self,
        tenant_id: UUID,
        project_id: UUID,
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "created_at",
        sort_order: int = -1,
    ) -> List[Route]:
        """Get routes by project with pagination"""
        return await self.route_repository.get_routes_by_project(
            tenant_id, project_id, skip, limit, sort_by, sort_order
        )

    async def search_routes(self, query: str, skip: int = 0, limit: int = 100) -> List[Route]:
        """Search routes by name or description"""
        return await self.route_repository.search_routes(query, skip, limit)
