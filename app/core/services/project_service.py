from typing import List, Optional
from uuid import UUID, uuid4

from app.common.request_context import get_request_context
from app.common.utils import get_current_date_time
from app.core.models.project import Project, NoteType, Collaborator
from app.core.models.paginated_response import PaginatedResponse
from app.core.models.request_log import RequestLog, RequestType, RequestStatus
from app.core.outbound.repositories.project_repository import ProjectRepository
from app.core.outbound.repositories.project_note_repository import ProjectNoteRepository
from app.core.outbound.repositories.request_log_repository import RequestLogRepository
from app.core.outbound.retrorunner.retro_runner_service import RetroRunnerService
from app.core.outbound.literature_service.ai_patent_processor import AIPatentProcessor
from app.core.outbound.storage.object_store import ObjectStore
from app.domain.chemistry.renderer import smiles_to_image


class ProjectService:
    """Service for handling project-related business logic"""

    def __init__(
        self,
        project_repository: ProjectRepository,
        request_log_repository: RequestLogRepository,
        ai_patent_processor: AIPatentProcessor,
        retro_runner_service: RetroRunnerService,
        object_store: ObjectStore,
        project_note_repository: ProjectNoteRepository
    ):
        self.project_repository = project_repository
        self.request_log_repository = request_log_repository
        self.ai_patent_processor = ai_patent_processor
        self.retro_runner_service = retro_runner_service
        self.object_store = object_store
        self.project_note_repository = project_note_repository

    async def create_project(self, tenant_id: UUID, project: Project) -> Project:
        """Sends event_bus to retro runner and patent processor and if successful, creates a new project and returns the created project"""
        canonical_smiles = project.canonical_smiles
        try:
            image_bytes = smiles_to_image(canonical_smiles)
        except Exception as e:
            raise RuntimeError(f"Smiles Renderer error: {str(e)}")

        try:
            blob_url = await self.object_store.upload_to_object_store(
                image_bytes=image_bytes, blob_name=f"{uuid4()}.png"
            )
        except Exception as e:
            raise RuntimeError(f"Image Upload error: {str(e)}")

        project.id = uuid4()
        project.molecule_image_url = blob_url

        retro_request_id = uuid4()
        retrosynthesis_task_payload = {
            "request_id": retro_request_id,
            "target_smiles": project.canonical_smiles,
            "molecule_name": project.compound_name,
            "tenant_id": project.tenant_id,
            "user_id": project.created_by,
        }

        # TODO: Need to fix while testing
        retro_result = await self.retro_runner_service.trigger_retrosynthesis(retrosynthesis_task_payload)

        if not retro_result:
            raise Exception("Retro task failed to insert into the queue")

        created_at = get_current_date_time()

        await self.request_log_repository.create_log(
            RequestLog(
                id=retro_request_id,
                tenant_id=tenant_id,
                project_id=project.id,
                user_id=project.created_by,
                request_type=RequestType.RETRO,
                status=RequestStatus.QUEUED,
                created_at=created_at,
                updated_at=created_at,
            )
        )

        patent_request_id = uuid4()
        patent_task_payload = {
            "request_id": patent_request_id,
            "target_smiles": project.canonical_smiles,
            "molecule_name": project.compound_name,
            "tenant_id": project.tenant_id,
            "user_id": project.created_by
        }
        patent_result = await self.ai_patent_processor.trigger_patent_processing(patent_task_payload)

        if not patent_result:
            raise Exception("Patent task failed to insert into the queue")

        await self.request_log_repository.create_log(
            RequestLog(
                id=patent_request_id,
                tenant_id=tenant_id,
                project_id=project.id,
                user_id=project.created_by,
                request_type=RequestType.PATENT,
                status=RequestStatus.QUEUED,
                created_at=created_at,
                updated_at=created_at,
            )
        )

        project.collaborators = {
            str(project.created_by): Collaborator(
                user_id=str(project.created_by),
                user_name=project.owner_name,
                email="",
                role="Owner",
                role_id="",
                permissions=[],
                active=True,
            )
        }
        return await self.project_repository.create_project(tenant_id, project)

    async def update_project(self, tenant_id: UUID, id: UUID, project: Project) -> Project:
        """Update an existing project"""
        tenant_id, user_id = get_request_context()
        # create a dict from project
        project_dict = project.model_dump()
        # Dont update the below keys
        for key in Project.get_keys_not_allowed_to_update():
            project_dict.pop(key, None)
        project_dict["updated_at"] = get_current_date_time()
        project_dict["updated_by"] = user_id
        updated_project = await self.project_repository.update_project(tenant_id, id, project_dict)
        return updated_project

    async def search_projects(self, tenant_id: UUID, query: str, skip: int = 0, limit: int = 100) -> PaginatedResponse[Project]:
        """Search projects by name or compound name"""
        return await self.project_repository.search_projects(tenant_id, query, skip, limit)

    async def get_projects_by_tenant(self, tenant_id: UUID, additional_filters: Optional[dict] = None, skip: int = 0, limit: int = 100) -> PaginatedResponse[Project]:
        """Get projects by tenant"""
        paginated_projects = await self.project_repository.get_projects_by_tenant(tenant_id, additional_filters, skip, limit)

        project_request_status_data = await self.request_log_repository.get_project_request_status(tenant_id, [RequestType.RETRO, RequestType.PATENT])
        default_data = {RequestType.RETRO: RequestStatus.QUEUED, RequestType.PATENT: RequestStatus.QUEUED}

        for project in paginated_projects.data:
            request_data = project_request_status_data.get(str(project.id), default_data)
            project.pathway_generation_status = request_data.get(RequestType.RETRO)
            project.patent_generation_status = request_data.get(RequestType.PATENT)
            project.collaborators = {k: v.model_dump() for k, v in project.collaborators.items() if v.active}

        return paginated_projects

    async def get_project(self, tenant_id: UUID, id: UUID) -> Optional[Project]:
        """Get a project by ID"""
        project = await self.project_repository.get_project(tenant_id, id)
        project_request_status_data = await self.request_log_repository.get_project_request_status(tenant_id, [RequestType.RETRO, RequestType.PATENT], [project.id])
        
        default_data = {RequestType.RETRO: RequestStatus.QUEUED, RequestType.PATENT: RequestStatus.QUEUED}
        request_data = project_request_status_data.get(str(project.id), default_data)
        project.pathway_generation_status = request_data.get(RequestType.RETRO)
        project.patent_generation_status = request_data.get(RequestType.PATENT)
        project.shortlisted_claims_count = await self.project_note_repository.get_shortlisted_claim_count(tenant_id, id, NoteType.PATENT_CLAIMS)
        project.collaborators = {k: v.model_dump() for k, v in project.collaborators.items() if v.active}
        return project

    async def delete_project(self, tenant_id: UUID, id: UUID) -> bool:
        """Delete a project by ID"""
        return await self.project_repository.delete_project(tenant_id, id)

    async def add_collaborator_to_project(
        self, tenant_id: UUID, project_id: UUID, collaborator: Collaborator
    ) -> Project:
        """Assign a project to a user"""
        project = await self.project_repository.get_project(tenant_id, project_id)
        assignee_user_id = collaborator.user_id
        if not project:
            raise Exception("Project not found")

        if project.owner == assignee_user_id:
            raise Exception("Cannot assign the project owner as a collaborator")

        collaborators = project.collaborators or {}
        if assignee_user_id in collaborators:
            if collaborators[assignee_user_id].active:
                raise Exception("User is already a collaborator for this project")
            else:
                collaborators[assignee_user_id].active = True
        else:
            collaborators[assignee_user_id] = Collaborator(
                user_id=collaborator.user_id,
                user_name=collaborator.user_name,
                email=collaborator.email,
                role=collaborator.role,
                role_id=collaborator.role_id,
                permissions=collaborator.permissions,
                active=True,
            )
        project.collaborators = collaborators
        updated_project = await self.project_repository.update_project(tenant_id, project_id, project.model_dump())
        updated_project.collaborators = {
            k: v.model_dump() for k, v in updated_project.collaborators.items() if v.active
        }
        return updated_project

    async def remove_collaborator_from_project(self, tenant_id: UUID, project_id: UUID, user_id: str) -> Project:
        """Unassign a project from a user"""
        project = await self.project_repository.get_project(tenant_id, project_id)
        if not project:
            raise Exception("Project not found")

        if project.owner == user_id:
            raise Exception("Cannot unassign the project owner")

        collaborators = project.collaborators or {}
        if str(user_id) not in collaborators:
            raise Exception("User is not a collaborator for this project")

        if not collaborators[str(user_id)].active:
            raise Exception("User is already unassigned from this project")

        collaborators[user_id].active = False
        project.collaborators = collaborators
        updated_project = await self.project_repository.update_project(tenant_id, project_id, project.model_dump())
        updated_project.collaborators = {
            k: v.model_dump() for k, v in updated_project.collaborators.items() if v.active
        }
        return updated_project
