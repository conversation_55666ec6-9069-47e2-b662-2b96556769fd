from typing import List, Optional, Dict, Any
from datetime import datetime

from app.core.models.reaction import Reaction
from app.core.outbound.repositories.reaction_repository import ReactionRepository


class ReactionService:
    """Service for handling reaction-related business logic"""

    def __init__(self, reaction_repository: ReactionRepository):
        self.reaction_repository = reaction_repository

    async def create_reaction(self, reaction: Reaction) -> str:
        """Create a new reaction"""
        return await self.reaction_repository.create_reaction(reaction)

    async def update_reaction(self, id: str, reaction: Reaction) -> bool:
        """Update an existing reaction"""
        return await self.reaction_repository.update_reaction(id, reaction)

    async def get_reactions_by_step(
        self, step_id: str, skip: int = 0, limit: int = 100, sort_by: str = "created_at", sort_order: int = -1
    ) -> List[Reaction]:
        """Get reactions by step with pagination"""
        return await self.reaction_repository.get_reactions_by_step(step_id, skip, limit, sort_by, sort_order)

    async def get_reactions_by_status(self, status: str, skip: int = 0, limit: int = 100) -> List[Reaction]:
        """Get reactions by status"""
        return await self.reaction_repository.get_reactions_by_status(status, skip, limit)

    async def get_reactions_by_tags(self, tags: List[str], skip: int = 0, limit: int = 100) -> List[Reaction]:
        """Get reactions by tags"""
        return await self.reaction_repository.get_reactions_by_tags(tags, skip, limit)

    async def search_reactions(self, query: str, skip: int = 0, limit: int = 100) -> List[Reaction]:
        """Search reactions by name or description"""
        return await self.reaction_repository.search_reactions(query, skip, limit)

    async def get_reaction_stats(self) -> Dict[str, Any]:
        """Get reaction statistics"""
        return await self.reaction_repository.get_reaction_stats()
