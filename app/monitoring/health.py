from fastapi import APIRouter
from typing import Dict

health_router = APIRouter(tags=["Monitoring"])


@health_router.get("/health")
async def health_check() -> Dict[str, str]:
    return {"status": "healthy"}


@health_router.get("/health/ready")
async def readiness_check():
    try:
        return {"status": "ready"}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}


@health_router.get("/health/live")
async def liveness_check():
    return {"status": "live"}
