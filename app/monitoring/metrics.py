from prometheus_client import Counter, Histogram, Info, CONTENT_TYPE_LATEST, generate_latest, REGISTRY
from fastapi import Request, Response, APIRouter
from app.config.app_config import app_config
import time

def cleanup(project_name: str):
    return project_name.replace("-", "_")

# Metrics definitions
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

REQUEST_LATENCY = Histogram(
    'http_request_duration_seconds',
    'HTTP request latency',
    ['method', 'endpoint']
)

SERVICE_INFO = Info(cleanup(app_config.PROJECT_NAME), 'Microservice information')

# Create metrics router
metrics_router = APIRouter(tags=["Monitoring"])

@metrics_router.get("/metrics")
async def metrics():
    """
    Endpoint that serves Prometheus metrics
    """
    return Response(
        generate_latest(REGISTRY),  # Pass the REGISTRY here
        media_type=CONTENT_TYPE_LATEST
    )

async def metrics_middleware(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    
    # Skip metrics endpoint from being recorded
    if request.url.path != "/metrics":
        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.url.path,
            status=response.status_code
        ).inc()
        
        REQUEST_LATENCY.labels(
            method=request.method,
            endpoint=request.url.path
        ).observe(time.time() - start_time)
    
    return response

# Initialize service info
def init_service_info():
    SERVICE_INFO.info({
        'version': app_config.version,
        'environment': app_config.env,
        'name': cleanup(app_config.PROJECT_NAME)
    })


