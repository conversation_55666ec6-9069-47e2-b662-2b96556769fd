import os

from dotenv import load_dotenv
from pydantic_settings import BaseSettings

# Load default environment variables
load_dotenv("default.env")

# Load root environment variables
root_env_file = ".env"
load_dotenv(root_env_file)

# Load environment variables
env_file = f".env.{os.getenv('ENV', 'prod')}"
if os.path.exists(env_file):
    load_dotenv(env_file)


class AppConfig(BaseSettings):
    ENV: str
    PROJECT_NAME: str
    LOGGING_LEVEL: str
    BASE_PATH: str
    TZ: str

    # Database Settings
    CHEMSTACK_MONGODB_CONNECTION_STRING: str
    CHEMSTACK_MONGODB_DB_NAME: str

    RETRO_MONGODB_CONNECTION_STRING: str
    RETRO_MONGODB_DB_NAME: str

    AZURE_SEARCH_ENDPOINT: str
    AZURE_SEARCH_API_KEY: str
    AZURE_SEARCH_INDEX: str
    AZURE_SEARCH_API_VERSION: str

    COMMON_MONGODB_MAX_POOL_SIZE: int
    COMMON_MONGODB_MIN_POOL_SIZE: int
    COMMON_MONGODB_MAX_IDLE_TIME_MS: int
    COMMON_MONGODB_CONNECT_TIMEOUT_MS: int
    COMMON_MONGODB_SERVER_SELECTION_TIMEOUT_MS: int
    COMMON_MONGODB_AUTH_SOURCE: str

    # TODO: Remove this integration after prashanth's changes in agent-hub is merged
    OPENAI_API_KEY: str

    # Redis Settings
    REDIS_URL: str
    RETRO_REQUEST_REDIS_QUEUE: str
    RETRO_RESPONSE_REDIS_QUEUE: str
    PATENT_REQUEST_REDIS_QUEUE: str
    PATENT_RESPONSE_REDIS_QUEUE: str

    AZURE_BLOB_CONNECTION_STRING: str
    AZURE_BLOB_CONTAINER_NAME: str

    CDN_URL: str


# Initialize settings
app_config = AppConfig()
