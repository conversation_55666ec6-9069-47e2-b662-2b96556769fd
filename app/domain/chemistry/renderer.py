import tempfile

from indigo import Indigo
from indigo.renderer import IndigoRenderer

# Create Indigo and IndigoRenderer instances at once
_indigo = Indigo()
_renderer = IndigoRenderer(_indigo)

def smiles_to_image(smiles: str) -> bytes:
    if ">>" in smiles:
        reaction = _indigo.loadReaction(smiles)
    else:
        reaction = _indigo.loadMolecule(smiles)
    with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
        _renderer.renderToFile(reaction, tmp_file.name)
        tmp_file.seek(0)
        image_bytes = tmp_file.read()

    return image_bytes
