import asyncio

from celery import Celery
from celery.signals import worker_process_init, worker_process_shutdown
from loguru import logger

from app.config.app_config import app_config
from app.infra.mongo_client.chemstack_db_client import chemstack_db_client

celery_consumer_app = Celery('chemstack-celery-consumer',
                             broker=app_config.REDIS_URL,
                             backend=app_config.REDIS_URL,
                             include=[
                                 'app.api.workers.patent_response_listener',
                                 'app.api.workers.retro_response_listener',
                             ]
                             )

celery_consumer_app.conf.update(task_serializer="json",
                                accept_content=["json"],
                                result_serializer="json",
                                timezone="UTC",
                                enable_utc=True,
                                task_track_started=True,
                                task_time_limit=30 * 60,  # 30 minutes
                                task_soft_time_limit=25 * 60,  # 25 minutes
                                worker_prefetch_multiplier=1,
                                worker_max_tasks_per_child=1000,
                                result_expires=3600,  # 1 hour
                                broker_connection_retry_on_startup=True,
                                task_acks_late=True,
                                worker_disable_rate_limits=True,
                                worker_direct=True,
                                task_always_eager=False,
                                task_eager_propagates=True,
                                task_default_queue="celery",
                                task_queues={
                                    "celery": {"exchange": "celery", "routing_key": "celery"},
                                    app_config.PATENT_RESPONSE_REDIS_QUEUE: {
                                        "exchange": app_config.PATENT_RESPONSE_REDIS_QUEUE,
                                        "routing_key": app_config.PATENT_RESPONSE_REDIS_QUEUE},
                                    app_config.RETRO_RESPONSE_REDIS_QUEUE: {
                                        "exchange": app_config.RETRO_RESPONSE_REDIS_QUEUE,
                                        "routing_key": app_config.RETRO_RESPONSE_REDIS_QUEUE},
                                },
                                )

celery_consumer_app.conf.update(worker_log_format="[%(asctime)s: %(levelname)s/%(processName)s] %(message)s",
                                worker_task_log_format="[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s"
                                )


# Celery signal to ensure each worker process has a persistent event loop and MongoDB connection
@worker_process_init.connect
def fix_asyncio_event_loop(**kwargs):
    """Ensure each worker process has a persistent event loop and MongoDB connection"""
    try:
        asyncio.get_event_loop()
    except RuntimeError:
        asyncio.set_event_loop(asyncio.new_event_loop())
        logger.info("Created new event loop for worker process")

    # Connect to MongoDB for this worker process
    try:
        loop = asyncio.get_event_loop()
        loop.run_until_complete(chemstack_db_client.connect())
        logger.info("Connected to MongoDB for worker process")
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB for worker process: {e}")


@worker_process_shutdown.connect
def cleanup_worker(**kwargs):
    """Cleanup MongoDB connection when a worker process shuts down"""
    try:
        loop = asyncio.get_event_loop()
        loop.run_until_complete(chemstack_db_client.disconnect())
        logger.info("Disconnected from MongoDB for worker process")
    except Exception as e:
        logger.error(f"Error disconnecting from MongoDB for worker process: {e}")


if __name__ == "__main__":
    logger.info("Starting Celery consumer...")
    celery_consumer_app.start()
