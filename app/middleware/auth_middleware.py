from fastapi import Request, Response
from fastapi.responses import JSONResponse
from app.common.request_context import (
    user_id_ctx,
    tenant_id_ctx,
    user_roles_ctx,
)
from app.api.schemas.common import ErrorResponse, ErrorDetails, ErrorCode
from loguru import logger
import re

USER_ID_HEADER = "x-user-id"
TENANT_ID_HEADER = "x-tenant-id"
USER_ROLES_HEADER = "x-user-roles"


async def auth_middleware(request: Request, call_next):
    """Middleware to handle authentication and set request context."""
    # Skip authentication for paths that don't start with /api/
    if not re.match(r"^/chemstack/api/.*", request.url.path):
        logger.info(f"Skipping authentication for {request.url.path}")
        return await call_next(request)

    try:
        user_id = request.headers.get(USER_ID_HEADER)
        tenant_id = request.headers.get(TENANT_ID_HEADER)
        user_roles = request.headers.get(USER_ROLES_HEADER)
        if not user_id or not tenant_id:
            return JSONResponse(
                status_code=401,
                content=ErrorResponse(
                    success=False,
                    error=ErrorDetails(
                        code=ErrorCode.UNAUTHORIZED,
                        message="Unauthorized",
                        details={"reason": "Missing user id or tenant id"},
                    ),
                ).model_dump(),
            )

        user_id_ctx.set(user_id)
        tenant_id_ctx.set(tenant_id)
        if user_roles:
            user_roles_ctx.set(user_roles.split(","))
        # Continue with the request

        response = await call_next(request)
        return response

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                success=False,
                error=ErrorDetails(
                    code=ErrorCode.INTERNAL_ERROR,
                    message="Internal server error",
                    details={"error": str(e)},
                ),
            ).model_dump(),
        )
    finally:
        # Clean up context
        # user_id_ctx.reset()
        # tenant_id_ctx.reset()
        # organization_id_ctx.reset()
        pass
