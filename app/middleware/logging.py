from fastapi import Request
from loguru import logger
import time

async def logging_middleware(request: Request, call_next):
    start_time = time.time()
    logger.info(f"Request started: {request.method} {request.url}")

    response = await call_next(request)

    process_time = time.time() - start_time
    logger.info(
        f"Request completed: {request.method} {request.url} "
        f"Status: {response.status_code} "
        f"Duration: {process_time:.3f}s"
    )

    return response