from contextvars import Con<PERSON><PERSON><PERSON>
from typing import <PERSON><PERSON>, <PERSON><PERSON>, List
from uuid import <PERSON><PERSON><PERSON>
from fastapi import HTTPException

# Context variables for request-scoped data
user_id_ctx: ContextVar[Optional[UUID]] = ContextVar("user_id", default=None)
tenant_id_ctx: ContextVar[Optional[UUID]] = ContextVar("tenant_id", default=None)
user_roles_ctx: ContextVar[Optional[List[str]]] = ContextVar("user_roles", default=None)


def get_current_user_id() -> Optional[UUID]:
    """Get the current user ID from context."""
    return user_id_ctx.get()


def get_current_tenant_id() -> Optional[UUID]:
    """Get the current tenant ID from context."""
    return tenant_id_ctx.get()

def get_current_user_roles() -> Optional[List[str]]:
    """Get the current user roles from context."""
    return user_roles_ctx.get()

def get_request_context() -> <PERSON>ple[UUID, UUID]:
    """Get and validate both tenant and user IDs from context.

    Returns:
        Tuple[UUID, UUID]: A tuple containing (tenant_id, user_id)

    Raises:
        HTTPException: If either tenant_id or user_id is missing from context
    """
    tenant_id = get_current_tenant_id()
    user_id = get_current_user_id()

    if not tenant_id:
        raise HTTPException(status_code=401, detail="Tenant ID not found in context")
    if not user_id:
        raise HTTPException(status_code=401, detail="User ID not found in context")

    return tenant_id, user_id
