from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from loguru import logger
from app.api.schemas.common import ErrorResponse, ErrorDetails, ErrorCode


def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP exception handler for FastAPI HTTP exceptions"""
    error_detail = exc.detail
    if isinstance(error_detail, dict):
        # If the detail is already in our format, use it directly
        if "code" in error_detail and "message" in error_detail:
            return JSONResponse(
                status_code=exc.status_code,
                content=ErrorResponse(
                    success=False,
                    error=ErrorDetails(
                        code=error_detail["code"],
                        message=error_detail["message"],
                        details=error_detail.get("details", {}),
                    ),
                ).model_dump(),
            )

    # If the detail is a simple string or not in our format, wrap it
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            success=False,
            error=ErrorDetails(
                code=ErrorCode.UNAUTHORIZED if exc.status_code == 401 else ErrorCode.INVALID_REQUEST,
                message=str(error_detail),
                details={},
            ),
        ).model_dump(),
    )


def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled exceptions"""
    # Check if it's an HTTPException and route it to the HTTP handler
    if isinstance(exc, HTTPException):
        return http_exception_handler(request, exc)
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            success=False,
            error=ErrorDetails(
                code=ErrorCode.INTERNAL_ERROR, message="An unexpected error occurred", details={"error": str(exc)}
            ),
        ).model_dump(),
    )
