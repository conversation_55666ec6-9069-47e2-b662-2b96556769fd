from app.core.models.retro_route import RetroRoute
from app.infra.retrorunner.db.retro_route_entity import RetroRouteEntity


class RetroRouteMapper:
    """Mapper for converting between Route domain model and RouteEntity"""

    def to_domain_model(self, entity: RetroRouteEntity) -> RetroRoute:
        """Convert RetroRouteEntity to RetroRoute domain model"""
        if not entity:
            return None

        return RetroRoute(
            id=str(entity._id) if entity._id else None,
            request_id=str(entity.request_id) if entity.request_id is not None else None,
            data=getattr(entity, "data", []),
            route_name=entity.route_name,
            num_steps=entity.num_steps,
            route_cost=entity.route_cost,
            certainty=entity.certainty,
            total_route_score=entity.total_route_score,
            tags=entity.tags,
            created_by=str(entity.created_by) if entity.created_by else None,
            updated_by=str(entity.updated_by) if entity.updated_by else None,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
            raw_reactants=getattr(entity, "raw_reactants", []),
            route_reaction_img=getattr(entity, "route_reaction_img", None),
            route_id=getattr(entity, "route_id", None),
            unique_id=(
                str(getattr(entity, "unique_id", None)) if getattr(entity, "unique_id", None) is not None else None
            ),
        )

    def to_entity_model(self, domain_model: RetroRoute) -> RetroRouteEntity:
        """Convert RetroRoute domain model to RetroRouteEntity"""
        if not domain_model:
            return None

        return RetroRouteEntity(
            _id=domain_model.id if domain_model.id else None,
            tenant_id=domain_model.tenant_id,
            project_id=domain_model.project_id,
            route_name=domain_model.route_name,
            status=domain_model.status,
            num_steps=domain_model.num_steps,
            raw_reactants=domain_model.raw_reactants,
            route_reaction_img=domain_model.route_reaction_img,
            route_cost=domain_model.route_cost,
            certainty=domain_model.certainty,
            total_route_score=domain_model.total_route_score,
            tags=domain_model.tags,
            created_by=domain_model.created_by if domain_model.created_by else None,
            updated_by=domain_model.updated_by if domain_model.updated_by else None,
            created_at=domain_model.created_at,
            updated_at=domain_model.updated_at,
            route_id=domain_model.route_id,
            unique_id=domain_model.unique_id,
        )
