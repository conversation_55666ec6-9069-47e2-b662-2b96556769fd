from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID

from app.infra.common.db.base_entity import BaseEntity


class RetroRouteEntity(BaseEntity):
    """Retro Route entity"""

    def __init__(
        self,
        _id: Optional[UUID] = None,
        route_name: str = None,
        data: Optional[List[Dict[str, Any]]] = None,
        status: str = None,
        num_steps: int = 0,
        route_cost: Optional[float] = None,
        certainty: Optional[float] = None,
        total_route_score: Optional[float] = None,
        tags: List[str] = None,
        created_by: Optional[UUID] = None,
        updated_by: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
        raw_reactants: List[str] = None,
        route_reaction_img: Optional[str] = None,
        request_id: Optional[str] = None,
        target_smiles: Optional[str] = None,
        route_id: Optional[int] = None,
        unique_id: Optional[str] = None,
    ):
        super().__init__(_id=_id, created_at=created_at, updated_at=updated_at)
        self.request_id = request_id
        self.route_name = route_name
        self.data = data
        self.status = status
        self.num_steps = num_steps
        self.raw_reactants = raw_reactants or []
        self.route_reaction_img = route_reaction_img
        self.route_cost = route_cost
        self.certainty = certainty
        self.total_route_score = total_route_score
        self.tags = tags or []
        self.created_by = created_by
        self.updated_by = updated_by
        self.target_smiles = target_smiles
        self.route_id = route_id
        self.unique_id = unique_id

    def to_dict(self) -> dict:
        """Convert entity to dictionary"""
        data = super().to_dict()
        data.update(
            {
                "request_id": self.request_id,
                "route_name": self.route_name,
                "data": self.data,
                "description": self.description,
                "status": self.status,
                "num_steps": self.num_steps,
                "raw_reactants": self.raw_reactants,
                "route_reaction_img": self.route_reaction_img,
                "route_cost": self.route_cost,
                "certainty": self.certainty,
                "total_route_score": self.total_route_score,
                "tags": self.tags,
                "target_smiles": self.target_smiles,
                "route_id": self.route_id,
                "unique_id": self.unique_id,
            }
        )
        return {k: v for k, v in data.items() if v is not None}

    @classmethod
    def from_dict(cls, data: dict) -> "RetroRouteEntity":
        """Create entity from dictionary"""
        _id = data.get("_id")
        if isinstance(_id, str):
            try:
                _id = UUID(_id)
            except ValueError:
                pass

        return cls(
            _id=_id,
            request_id=data.get("request_id"),
            route_name=data.get("route_name"),
            data=data.get("data"),
            status=data.get("status"),
            num_steps=data.get("num_steps", 0),
            raw_reactants=data.get("raw_reactants"),
            route_reaction_img=data.get("route_reaction_img"),
            route_cost=data.get("route_cost"),
            certainty=data.get("certainty"),
            total_route_score=data.get("total_route_score"),
            tags=data.get("tags", []),
            created_at=data.get("created_at"),
            updated_at=data.get("updated_at"),
            target_smiles=data.get("target_smiles"),
            route_id=data.get("route_id"),
            unique_id=str(data.get("unique_id")) if data.get("unique_id") is not None else None,
        )

    async def log_entities(cursor):
        entities = []
        async for entity_dict in cursor:
            entity = RetroRouteEntity.from_dict(entity_dict)
            # Assuming you have access to the mapper from here or can instantiate it
            entities.append(RetroRouteEntity.mapper.to_domain_model(entity))
