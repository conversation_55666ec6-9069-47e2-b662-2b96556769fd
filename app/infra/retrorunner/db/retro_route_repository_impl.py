from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID
from motor.motor_asyncio import Async<PERSON><PERSON>otorCollection
from app.config.app_config import app_config
from app.core.models.retro_route import RetroRoute, RetroRouteFilters, RouteStatus
from app.infra.retrorunner.db.retro_route_entity import RetroRouteEntity
from app.infra.retrorunner.db.retro_route_mapper import RetroRouteMapper
from app.infra.mongo_client.retro_db_client import retro_db_client
from app.core.models.paginated_response import PaginatedResponse


class RetroRouteRepositoryImpl:
    """MongoDB implementation of route repository operations"""

    def __init__(self):
        """Initialize route repository"""
        self._collection = retro_db_client.db.retro_data
        self.mapper = RetroRouteMapper()

    async def get_retro_routes_by_request_id(
        self,
        tenant_id: UUID,
        project_id: UUID,
        request_ids: List[UUID],
        raw_reactants: Optional[List[str]] = None,
        reactions: Optional[List[str]] = None,
        reagents: Optional[List[str]] = None,
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "total_route_score",
        sort_order: int = -1,
        retro_route_ids: Optional[List[UUID]] = None,
    ) -> PaginatedResponse[RetroRoute]:
        """Get routes by project ID with sorting and optional retro_route_ids filter"""
        query = {"request_id": {"$in": [str(rid) for rid in request_ids]}}

        if retro_route_ids is not None:
            query["_id"] = {"$in": [str(rid) for rid in retro_route_ids]}
 
        if raw_reactants:
            query["raw_reactants"] = {"$in": raw_reactants}
        
        if reagents:
            query["data.other_information.reagents_and_solvents"] = {"$elemMatch": {"name": {"$in": reagents}}}
        
        if reactions:
            query["data.rxn_class.reaction_name"] = {"$in": reactions}

        # Only return routes with route_status 'completed'
        query["route_status"] = RouteStatus.COMPLETED

        # Get total count with the current query
        total_rows = await self._collection.count_documents(query)

        ALLOWED_SORT_FIELDS = {
            "total_route_score": "total_route_score",
            "steps": "num_steps",
            "cost": "route_cost",
            "created_at": "created_at",
        }
        sort_field = ALLOWED_SORT_FIELDS.get(sort_by, "total_route_score")

        cursor = self._collection.find(query).sort(sort_field, sort_order).skip(skip).limit(limit)
        entities = []
        async for entity_dict in cursor:
            entity = RetroRouteEntity.from_dict(entity_dict)
            entities.append(self.mapper.to_domain_model(entity))

        return PaginatedResponse(data=entities, total=total_rows, skip=skip, limit=limit)

    async def get_retro_route_by_id(self, id: UUID) -> Optional[RetroRoute]:
        """Get a route by its ID"""
        doc = await self._collection.find_one({"_id": str(id)})
        if doc:
            entity = RetroRouteEntity.from_dict(doc)
            return self.mapper.to_domain_model(entity)
        return None

    async def get_retro_route_filters(self, request_id: List[UUID]) -> RetroRouteFilters:
        pipeline = [
            {"$match": {"request_id": {"$in": [str(rid) for rid in request_id]}, "route_status": RouteStatus.COMPLETED}},
            {
                "$facet": {
                    "building_blocks": [
                        {"$unwind": "$raw_reactants"},
                        {"$group": {"_id": "$raw_reactants"}},
                        {"$group": {"_id": None, "all_values": {"$addToSet": "$_id"}}},
                    ],
                    "reactions": [
                        {"$unwind": "$data"},
                        {"$group": {"_id": "$data.rxn_class.reaction_name"}},
                        {"$match": {"_id": {"$ne": None}}},
                        {"$group": {"_id": None, "all_values": {"$addToSet": "$_id"}}},
                    ],
                    "reagents": [
                        {"$unwind": "$data"},
                        {"$unwind": "$data.other_information.reagents_and_solvents"},
                        {"$group": {"_id": "$data.other_information.reagents_and_solvents.name"}},
                        {"$match": {"_id": {"$ne": None}}},
                        {"$group": {"_id": None, "all_values": {"$addToSet": "$_id"}}},
                    ],
                }
            },
            {
                "$project": {
                    "building_blocks": {"$ifNull": [{"$first": "$building_blocks.all_values"}, []]},
                    "reactions": {"$ifNull": [{"$first": "$reactions.all_values"}, []]},
                    "reagents": {"$ifNull": [{"$first": "$reagents.all_values"}, []]},
                }
            },
        ]

        result = await self._collection.aggregate(pipeline).to_list(1)

        if not result:
            return RetroRouteFilters(reactions=[], reagents=[], building_blocks=[])

        data = result[0]
        return RetroRouteFilters(
            reactions=sorted(data.get("reactions", [])),
            reagents=sorted(data.get("reagents", [])),
            building_blocks=sorted(data.get("building_blocks", [])),
        )
