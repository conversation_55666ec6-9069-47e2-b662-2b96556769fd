from app.core.outbound.retrorunner.retro_runner_service import RetroRunnerService
from uuid import UUID
from app.core.models.retro_route import RetroRoute
from typing import List, Optional, Dict, Any
from app.infra.retrorunner.db.retro_route_repository_impl import RetroRouteRepositoryImpl
from app.config.app_config import app_config
from app.infra.common.celery_producer import CeleryProducer
from app.core.models.retro_route import RetroRouteFilters
from app.core.models.paginated_response import PaginatedResponse


class RetroRunnerServiceImpl(RetroRunnerService):
    """Implementation of RetroRunnerService"""

    _celery_task_name = "process_retro_task"

    def __init__(self, retro_route_repository: RetroRouteRepositoryImpl):
        self.retro_route_repository = retro_route_repository
        self.retro_queue = app_config.RETRO_REQUEST_REDIS_QUEUE
        self.retro_producer = CeleryProducer(
            app_config.REDIS_URL, app_config.REDIS_URL)

    async def trigger_retrosynthesis(self, payload: Dict[str, Any]):
        return await self.retro_producer.send_task(self._celery_task_name, self.retro_queue, payload=payload)

    async def fetch_routes_by_request_id(
        self,
        tenant_id: UUID,
        project_id: UUID,
        request_ids: List[UUID],
        raw_reactants: Optional[List[str]] = None,
        reactions: Optional[List[str]] = None,
        reagents: Optional[List[str]] = None,
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "created_at",
        sort_order: int = -1,
        retro_route_ids: Optional[List[UUID]] = None,
    ) -> PaginatedResponse[RetroRoute]:
        return await self.retro_route_repository.get_retro_routes_by_request_id(
            tenant_id, project_id, request_ids, raw_reactants, reactions, reagents, skip, limit, sort_by, sort_order, retro_route_ids=retro_route_ids
        )

    async def fetch_by_request_id_and_retro_route_id(
        self, request_id: UUID, retro_route_id: UUID
    ) -> Optional[RetroRoute]:
        return await self.retro_route_repository.get_retro_route_by_id(retro_route_id)

    async def fetch_retro_route_filters(self, request_ids: List[UUID]) -> RetroRouteFilters:
        return await self.retro_route_repository.get_retro_route_filters(request_ids)
