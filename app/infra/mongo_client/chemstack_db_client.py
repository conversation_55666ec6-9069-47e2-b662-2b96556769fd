from app.config.app_config import app_config
from app.infra.common.db.base_mongo_db_client import BaseMongoDBClient


class ChemstackDBClient(BaseMongoDBClient):
    """ChemstackDB client singleton"""

    def get_connection_string(self) -> str:
        """Get the MongoDB connection string"""
        return app_config.CHEMSTACK_MONGODB_CONNECTION_STRING

    def get_database_name(self) -> str:
        """Get the MongoDB database name"""
        return app_config.CHEMSTACK_MONGODB_DB_NAME

    def get_client_name(self) -> str:
        """Get the client name for logging"""
        return "CHEMSTACK"

    def get_connection_config(self) -> dict:
        """Get the connection configuration parameters"""
        return {
            "max_pool_size": app_config.COMMON_MONGODB_MAX_POOL_SIZE,
            "min_pool_size": app_config.COMMON_MONGODB_MIN_POOL_SIZE,
            "max_idle_time_ms": app_config.COMMON_MONGODB_MAX_IDLE_TIME_MS,
            "connect_timeout_ms": app_config.COMMON_MONGODB_CONNECT_TIMEOUT_MS,
            "server_selection_timeout_ms": app_config.COMMON_MONGODB_SERVER_SELECTION_TIMEOUT_MS,
            "auth_source": app_config.COMMON_MONGODB_AUTH_SOURCE,
            "tlsAllowInvalidCertificates": True,
        }


chemstack_db_client = ChemstackDBClient()
