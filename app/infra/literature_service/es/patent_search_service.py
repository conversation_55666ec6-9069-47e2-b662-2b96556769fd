import json
import httpx
import logging
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>
from uuid import UUID
from fastapi import HTTPException
from app.config.app_config import app_config
from app.core.models.patent import Patent, PatentFilter, PatentSortBy, SortOrder
from app.core.models.paginated_response import PaginatedResponse
from app.infra.literature_service.mappers.patent_mapper import PatentMapper

logger = logging.getLogger()


class PatentSearchService:
    def __init__(self):
        self.query_url = f"{app_config.AZURE_SEARCH_ENDPOINT}/indexes/{app_config.AZURE_SEARCH_INDEX}/docs/search?api-version={app_config.AZURE_SEARCH_API_VERSION}"
        self.headers = {"Content-Type": "application/json", "api-key": app_config.AZURE_SEARCH_API_KEY}
        self.mapper = PatentMapper()

        self.patent_code_and_office_mapping = {
            "US": "US Patent (USPTO)",
            "EP": "EP Patent (European Patent Office)",
            "CN": "CN Patent (Chinese Patent Office)",
            "JP": "JP Patent (Japan Patent Office)",
            "GB": "GB Patent (UK Patent Office)",
            "DE": "DE Patent (German Patent Office)",
            "PCT": "PCT (Patent Cooperation Treaty)",
            "CA": "CA Patent (Canadian Patent Office)",
            "AU": "AU Patent (Australian Patent Office)",
            "KR": "KR Patent (Korean Patent Office)",
            "IN": "IN Patent (Indian Patent Office)",
            "BR": "BR Patent (Brazilian Patent Office)",
            "RU": "RU Patent (Russian Patent Office)",
            "FR": "FR Patent (French Patent Office)",
            "IT": "IT Patent (Italian Patent Office)",
            "ES": "ES Patent (Spanish Patent Office)",
            "CH": "CH Patent (Swiss Patent Office)",
            "SE": "SE Patent (Swedish Patent Office)",
            "NL": "NL Patent (Dutch Patent Office)",
            "AT": "AT Patent (Austrian Patent Office)"
        }

    async def get_patents_by_request_ids(
        self,
        request_ids: List[UUID],
        sort_by: PatentSortBy,
        sort_order: SortOrder,
        reaction_types: Optional[List[str]] = None,
        claim_types: Optional[List[str]] = None,
        filing_companies: Optional[List[str]] = None,
        patent_offices: Optional[List[str]] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> PaginatedResponse[Patent]:
        """Query Azure Search for all patents with a given request_id and optional filters"""
        filters = []
        request_id_filters = [f"request_id eq '{str(request_id)}'" for request_id in request_ids]

        if request_id_filters:
            filters.append(f"({' or '.join(request_id_filters)})")

        if reaction_types:
            filter_data = [f"tags/any(t: t eq '{reaction_type}')" for reaction_type in reaction_types]
            filters.append(f"({' or '.join(filter_data)})")

        if claim_types:
            filter_data = [f"claim_tags/any(t: t eq '{claim_type}')" for claim_type in claim_types]
            filters.append(f"({' or '.join(filter_data)})")

        if filing_companies:
            filter_data = [f"assignees/any(t: t eq '{filing_company}')" for filing_company in filing_companies]
            filters.append(f"({' or '.join(filter_data)})")

        search_params = {"search": "*"}

        if patent_offices:
            filter_data = [f"{patent_office}*" for patent_office in patent_offices]
            search_params['search'] = f"{' OR '.join(filter_data)}"
            search_params["searchFields"] = "patent_id"
            search_params["queryType"] = "full"

        filter_str = " and ".join(filters)

        request_body = {
            "filter": filter_str,
            "orderby": f"{sort_by.value} {sort_order.value}",
            "skip": skip,
            "top": limit,
            "count": True,
        }

        request_body.update(search_params)

        async with httpx.AsyncClient() as client:
            response = await client.post(self.query_url, headers=self.headers, json=request_body)
            status_code, response_data = response.status_code, response.json()
            if status_code != 200:
                logger.error(f"[AZURE SEARCH ERROR] Patent list - {json.dumps(response_data)}")
                raise HTTPException(status_code=status_code, detail="Something went wrong")
            
            logger.info(f"[AZURE SEARCH] List patent API call successful")

        results = [self.mapper.to_domain_model(
            item) for item in response_data.get("value", [])]
        total_count = response_data["@odata.count"]
        return PaginatedResponse(data=results, total=total_count, skip=skip, limit=limit)

    async def get_patent_by_id(self, patent_id: UUID) -> Optional[Patent]:
        """Query Azure Search for a specific patent by patent_id"""

        filter_str = f"id eq '{patent_id}'"

        request_body = {
            "search": "*",
            "filter": filter_str,
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(self.query_url, headers=self.headers, json=request_body)
            status_code, response_data = response.status_code, response.json()
            if status_code != 200:
                logger.error(f"[AZURE SEARCH ERROR] Single patent - {json.dumps(response_data)}")
                raise HTTPException(status_code=status_code, detail='Something went wrong')
            response_data = response.json().get("value")

        if response_data is None:
            raise KeyError('Missing value key in patent detail search')
        
        logger.info(f"[AZURE SEARCH] Single patent API call successful")

        try:
            patent_data = response_data[0]
        except:
            raise ValueError('Missing patent detail data')

        similar_patents = await self.get_similar_patents(patent_data.get('request_id'), patent_data.get("tags", []), patent_id)
        patent_data["similar_patents"] = similar_patents

        return self.mapper.to_domain_model(patent_data)

    async def get_similar_patents(
        self,
        request_id: UUID,
        patent_tags: List[str],
        exclude_patent_id: UUID,
        top: int = 3,
    ) -> List[Patent]:
        """Fetch top N similar patents for the given request_id and optional filters."""

        # Similar patents will only be checked against the same request id
        filter_clauses = [f"request_id eq '{str(request_id)}'"]

        # Match patents that share ANY tag of the original patent
        tag_filters = [f"tags/any(t: t eq '{tag}')" for tag in patent_tags]
        if tag_filters:
            filter_clauses.append(f"({' or '.join(tag_filters)})")

        # Exclude the current patent ID from the results
        if exclude_patent_id:
            filter_clauses.append(f"id ne '{exclude_patent_id}'")

        filter_str = " and ".join(filter_clauses)

        request_body = {
            "search": "*",
            "select": "id, patent_id, claim_tags, tags, title, publication_date, assignees, relevancyScore",
            "filter": filter_str,
            "orderby": f"{PatentSortBy.RELEVANCY_SCORE.value} {SortOrder.DESCENDING.value}",
            "top": top,
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(self.query_url, headers=self.headers, json=request_body)
            status_code, response_data = response.status_code, response.json()
            if status_code != 200:
                logger.error(f"[AZURE SEARCH ERROR] Similar patent data - {json.dumps(response_data)}")
                raise HTTPException(status_code=status_code, detail='Something went wrong')
            response_data = response.json().get("value")

        if response_data is None:
            raise KeyError('Missing value key in similar patent search')
        
        logger.info(f"[AZURE SEARCH] Similar Patent API call successful - {response_data}")

        return response_data

    async def get_patent_filter_data(self, request_ids: List[UUID]) -> PatentFilter:
        """Fetch patent filter data for the given request_id and optional filters."""

        request_id_filters = [
            f"request_id eq '{str(request_id)}'" for request_id in request_ids]
        filter_str = f"({' or '.join(request_id_filters)})"

        request_body = {
            "search": "*",
            "select": "patent_id",
            "filter": filter_str,
            "facets": [
                "tags,count:1000",
                "claim_tags,count:1000",
                "assignees,count:1000"
            ],
            "count": True
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(self.query_url, headers=self.headers, json=request_body)
            status_code, response_data = response.status_code, response.json()
            if status_code != 200:
                logger.error(f"[AZURE SEARCH ERROR] Patent filter data - {json.dumps(response_data)}")
                raise HTTPException(status_code=status_code, detail='Something went wrong')

        data_aggregation = response_data.get('@search.facets')
        if data_aggregation is None:
            raise KeyError(
                'Missing aggregation data in patent filter data search')
        
        logger.info(f"[AZURE SEARCH] Filter patent data API call successful")

        reaction_types = [data['value']
                          for data in data_aggregation.get('tags', [])]
        claim_types = [data['value']
                       for data in data_aggregation.get('claim_tags', [])]
        filing_companies = [data['value']
                            for data in data_aggregation.get('assignees', [])]

        patent_codes = set(data['patent_id'][:2]
                           for data in response_data.get('value', []))
        patent_offices = {
            patent_code: self.patent_code_and_office_mapping[patent_code] for patent_code in patent_codes}

        return PatentFilter(reaction_types=reaction_types, claim_types=claim_types,
                            filing_companies=filing_companies, patent_offices=patent_offices)

    async def get_patent_count_by_request_ids(
        self,
        request_ids: List[UUID]
    ) -> int:
        """Query Azure Search for the count of patents with a given request_id and optional filters"""

        request_id_filters = [f"request_id eq '{str(request_id)}'" for request_id in request_ids]
        filter_str = " or ".join(request_id_filters)

        # Prepare the request body
        request_body = {
            "search": "*",
            "filter": filter_str,
            "count": True,
            "top": 0  # Only fetch count, not documents
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(self.query_url, headers=self.headers, json=request_body)
            status_code, response_data = response.status_code, response.json()
            if status_code != 200:
                logger.error(f"[AZURE SEARCH ERROR] {json.dumps(response_data)}")
                raise HTTPException(status_code=status_code, detail="Something went wrong")
        logger.info(f"[AZURE SEARCH] Patent count API call successful and count is {response_data['@odata.count']}")

        return response_data["@odata.count"]
