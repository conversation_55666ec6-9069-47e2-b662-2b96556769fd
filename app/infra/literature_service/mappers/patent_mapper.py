from typing import Dict, List, Any
from datetime import datetime
from app.core.models.patent import Patent, Claim, DependentClaim, SimilarPatent


class PatentMapper:
    """Mapper for transforming Azure Search patent documents into domain models"""

    def __init__(self):
        pass

    def to_domain_model(self, data: Dict[str, Any]) -> Patent:
        """Convert Azure Search 'data' document to PatentModel"""
        
        # Any field having some error in being processed by the Patent Processor is having value as "Analysis failed"
        key_reactions_str = data.get("reactionsInClaims", "")
        key_reactions = self._parse_key_reactions(key_reactions_str)

        independent_claims, dependent_claims = self._parse_claims(data.get("claims", {}))

        similar_patents = []

        similar_patents_data = data.get("similar_patents", [])
        for patent in similar_patents_data:
            patent['patent_number'] = patent['patent_id']
            patent['relevancy_score'] = patent.get('relevancyScore')
            patent['publication_date'] = self._parse_datetime(patent.get('publication_date'))
            similar_patent_model = SimilarPatent.model_validate(patent)
            similar_patents.append(similar_patent_model)

        return Patent(
            id=data.get("id"),
            patent_number=data.get("patent_id", ""),
            request_id=data.get("request_id", ""),
            tags=data.get("tags", []),
            claim_tags=data.get("claim_tags", []),
            claim_summary=data.get("summaryOfClaims", ""),
            title=data.get("title", ""),
            key_reactions=key_reactions,
            publication_date=self._parse_datetime(data.get("publication_date")),
            assignees=data.get("assignees", []),
            abstract=data.get("abstract", ""),
            inventors=data.get("inventors", []),
            filing_date=self._parse_datetime(data.get("filing_date")),
            pdf_url=data.get("pdf_url", ""),
            independent_claims=independent_claims,
            dependent_claims=dependent_claims,
            similar_patents=similar_patents,
            relevancy_score=data.get("relevancyScore")
        )

    def _parse_key_reactions(self, text: str) -> List[str]:
        if isinstance(text, str):
            text = text.replace("Key Reactions:", "", 1).strip()
            return [r.strip("* ").strip() for r in text.split("\n") if r.strip()]
        return []

    def _parse_claims(self, claims_data: List[Dict[str, Any]]) -> tuple[List[Claim], List[Claim]]:
        independent = []
        dependent = []

        for claim in claims_data:
            id = claim.get("id", "")
            text = claim.get("claim", "")
            tag = claim.get("tag", "")
            is_dependent = claim.get("dependent", False)
            depends_on = claim.get("depends_on", "")
            
            model_dict = {"id": id, "text": text, "tag": tag}

            if is_dependent:
                dependent.append(DependentClaim(**model_dict, depends_on=depends_on))
            else:
                independent.append(Claim(**model_dict))

        return independent, dependent

    def _parse_datetime(self, value: Any) -> datetime | None:
        if isinstance(value, datetime):
            return value
        if isinstance(value, str):
            try:
                return datetime.fromisoformat(value)
            except ValueError:
                return None
        return None
