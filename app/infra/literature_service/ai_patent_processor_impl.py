from uuid import UUID
from typing import List, Dict, Any, Tuple
from app.core.models.patent import Patent, PatentFilter, PatentSortBy, SortOrder
from app.core.models.paginated_response import PaginatedResponse
from app.core.outbound.literature_service.ai_patent_processor import AIPatentProcessor
from app.infra.literature_service.es.patent_search_service import PatentSearchService
from app.infra.literature_service.mappers.patent_mapper import PatentMapper
from app.config.app_config import app_config
from app.infra.common.celery_producer import CeleryProducer


class AIPatentProcessorImpl(AIPatentProcessor):
    _celery_task_name = "process_patent_message"

    def __init__(self, patent_search_service: PatentSearchService):
        self.patent_search_service = patent_search_service
        self._mapper = PatentMapper()
        self.patent_processing_queue = app_config.PATENT_REQUEST_REDIS_QUEUE
        self.patent_processing_producer = CeleryProducer(app_config.REDIS_URL, app_config.REDIS_URL)

    async def trigger_patent_processing(self, payload: Dict[str, Any]):
        result = await self.patent_processing_producer.send_task(
            self._celery_task_name,
            self.patent_processing_queue,
            payload=payload,
        )
        return result

    async def get_patents_by_request_ids(
        self,
        request_ids: List[UUID],
        sort_by: PatentSortBy,
        sort_order: SortOrder,
        reaction_types: List[str],
        claim_types: List[str],
        filing_companies: List[str],
        patent_offices: List[str],
        skip: int,
        limit: int,
    ) -> PaginatedResponse[Patent]:
        return await self.patent_search_service.get_patents_by_request_ids(
            request_ids, sort_by, sort_order, reaction_types, claim_types, filing_companies, patent_offices, skip, limit
        )

    async def get_patent_by_id(self, patent_id: UUID) -> Patent:
        return await self.patent_search_service.get_patent_by_id(patent_id)

    async def get_patent_filter_data(self, request_ids: List[UUID]) -> PatentFilter:
        return await self.patent_search_service.get_patent_filter_data(request_ids)

    async def get_patent_count_by_request_ids(
        self,
        request_ids: List[UUID]
    ) -> int:
        return await self.patent_search_service.get_patent_count_by_request_ids(request_ids)
