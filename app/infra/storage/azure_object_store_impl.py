from azure.storage.blob import BlobServiceClient

from app.config.app_config import app_config
from app.core.outbound.storage.object_store import ObjectStore


class AzureObjectStore(ObjectStore):
    """Implementation of ObjectStoreManager using Azure Blob Storage"""

    async def upload_to_object_store(self, 
                                  image_bytes: bytes, 
                                  blob_name: str) -> str:

        """Upload bytes to blob storage and return the blob URL"""
        blob_service_client = BlobServiceClient.from_connection_string(app_config.AZURE_BLOB_CONNECTION_STRING)
        container_client = blob_service_client.get_container_client(app_config.AZURE_BLOB_CONTAINER_NAME)
        try:
            container_client.create_container()
        except:
            # Container already exists, ignore the error
            pass

        blob_client = container_client.get_blob_client(blob_name)
        blob_client.upload_blob(image_bytes, overwrite=True, content_type='image/png')
        blob_url = f"{app_config.CDN_URL}{blob_name}"
        return blob_url