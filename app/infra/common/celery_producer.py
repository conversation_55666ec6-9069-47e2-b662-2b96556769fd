from typing import Optional

from celery import Celery
from loguru import logger

from app.config.app_config import app_config
from typing import Dict, Any


class CeleryProducer:
    def __init__(self, broker_url: str, backend_url: str):
        self.broker_url = broker_url
        self.backend_url = backend_url
        self.celery_app = self._create_celery_app(broker_url, backend_url)

    @staticmethod
    def _create_celery_app(broker_url: str, backend_url: str) -> Celery:
        """Create and configure Celery app."""
        celery_app = Celery("chemstack-celery-producer", broker=broker_url, backend=backend_url)

        celery_app.conf.update(
            task_serializer="json", accept_content=["json"], result_serializer="json", timezone="UTC", enable_utc=True
        )

        logger.info("Celery app configured successfully")
        return celery_app

    async def send_task(self, task_name: str, queue: str, payload: Dict[str, Any]) -> bool:
        """Send a retro synthesis task to the queue"""
        try:
            result = self.celery_app.send_task(task_name, args=[payload], queue=queue)
            if result.id:
                return True

            logger.error(f"Celery task id not returned for payload: {payload}")
            return False

        except Exception as e:
            logger.exception(f"Failed to send task for payload: {payload} , Error:{e}")
            return False
