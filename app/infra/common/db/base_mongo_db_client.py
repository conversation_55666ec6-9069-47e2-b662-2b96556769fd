from abc import ABC, abstractmethod

from bson.binary import UuidRepresentation
from bson.codec_options import CodecOptions
from loguru import logger
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ConnectionFailure


class BaseMongoDBClient(ABC):
    """Base MongoDB client class with common functionality"""

    _instance = None
    _client: AsyncIOMotorClient = None
    _db = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BaseMongoDBClient, cls).__new__(cls)
        return cls._instance

    @abstractmethod
    def get_connection_string(self) -> str:
        """Get the MongoDB connection string"""
        pass

    @abstractmethod
    def get_database_name(self) -> str:
        """Get the MongoDB database name"""
        pass

    @abstractmethod
    def get_client_name(self) -> str:
        """Get the client name for logging"""
        pass

    @abstractmethod
    def get_connection_config(self) -> dict:
        """Get the connection configuration parameters"""
        pass

    async def connect(self):
        """Connect to MongoDB"""
        if self._client is None:
            try:
                # Get configuration
                connection_string = self.get_connection_string()
                db_name = self.get_database_name()
                client_name = self.get_client_name()
                config = self.get_connection_config()

                # Create a client with connection options
                self._client = AsyncIOMotorClient(
                    connection_string,
                    maxPoolSize=config.get("max_pool_size", 10),
                    minPoolSize=config.get("min_pool_size", 1),
                    maxIdleTimeMS=config.get("max_idle_time_ms", 30000),
                    connectTimeoutMS=config.get("connect_timeout_ms", 20000),
                    serverSelectionTimeoutMS=config.get("server_selection_timeout_ms", 30000),
                    authSource=config.get("auth_source", "admin"),
                    uuidRepresentation="standard",
                    tlsAllowInvalidCertificates=True,  # <-- Add this line
                    tls=True,  # <-- Add this if not already in your connection string    
                )

                # Get a database with UUID codec options
                codec_options = CodecOptions(uuid_representation=UuidRepresentation.STANDARD)
                self._db = self._client.get_database(db_name, codec_options=codec_options)

                # Verify connection
                await self._client.admin.command("ping")
                logger.info(f"Connected to {client_name} MongoDB database: {db_name}")

            except ConnectionFailure as e:
                logger.error(f"Failed to connect to MongoDB: {str(e)}")
                raise ConnectionError(f"Failed to connect to {client_name} MongoDB: {str(e)}")

    async def disconnect(self):
        """Disconnect from MongoDB"""
        if self._client is not None:
            self._client.close()
            self._client = None
            self._db = None
            client_name = self.get_client_name()
            logger.info(f"Disconnected from {client_name} MongoDB")

    @property
    def db(self):
        """Get database instance"""
        if self._db is None:
            client_name = self.get_client_name()
            raise RuntimeError(f"{client_name} MongoDB client not connected. Call connect() first.")
        return self._db

    @property
    def client(self):
        """Get a client instance"""
        if self._client is None:
            client_name = self.get_client_name()
            raise RuntimeError(f"{client_name} MongoDB client not connected. Call connect() first.")
        return self._client
