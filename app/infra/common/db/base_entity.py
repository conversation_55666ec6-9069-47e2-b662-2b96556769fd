from datetime import datetime, UTC
from typing import Optional
from uuid import UUID


class BaseEntity:
    """Base entity for MongoDB documents"""

    def __init__(
        self,
        _id: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ):
        self._id = _id
        self.created_at = created_at or datetime.now(UTC)
        self.updated_at = updated_at or datetime.now(UTC)

    def to_dict(self) -> dict:
        """Convert entity to dictionary"""
        return {
            "_id": str(self._id) if self._id else None,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "BaseEntity":
        """Create entity from dictionary"""
        return cls(
            _id=UUID(data["_id"]) if data.get("_id") else None,
            created_at=data.get("created_at"),
            updated_at=data.get("updated_at"),
        )