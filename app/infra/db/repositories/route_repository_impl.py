from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4

from motor.motor_asyncio import AsyncIOMotorCollection
from pymongo import DESCENDING

from app.common.utils import get_current_date_time
from app.core.models.route import Route
from app.core.outbound.repositories.route_repository import RouteRepository
from app.infra.db.entities.route_entity import RouteEntity
from app.infra.db.mappers.route_mapper import RouteMapper
from app.infra.mongo_client.chemstack_db_client import chemstack_db_client


class RouteRepositoryImpl(RouteRepository):
    """MongoDB implementation of route repository operations"""

    def __init__(self):
        """Initialize route repository"""
        self.collection_name = "routes"
        self.mapper = RouteMapper()
        self._collection: Optional[AsyncIOMotorCollection] = None

    @property
    def collection(self) -> AsyncIOMotorCollection:
        """Get MongoDB collection"""
        if self._collection is None:
            self._collection = chemstack_db_client.db[self.collection_name]
        return self._collection

    async def create_indexes(self):
        """Create indexes for routes collection"""
        await self.collection.create_index("tenant_id")
        await self.collection.create_index("project_id")
        await self.collection.create_index("name")
        await self.collection.create_index("status")
        await self.collection.create_index("created_at")
        await self.collection.create_index("tags")

    async def create_route(self, tenant_id: UUID, project_id: UUID, route: Route) -> Route:
        """Create a new route and return the created route"""
        route.tenant_id = tenant_id
        route.project_id = project_id
        route.id = uuid4()
        route.created_at = get_current_date_time()
        route.updated_at = route.created_at

        entity = self.mapper.to_entity_model(route)
        entity_dict = entity.to_dict()
        result = await self.collection.insert_one(entity_dict)
        route.id = str(result.inserted_id)
        return route

    async def update_route(self, tenant_id: UUID, project_id: UUID, id: UUID, route: Dict[str, Any]) -> Route:
        """Update an existing route and return the updated route"""
        await self.collection.update_one(
            {"_id": str(id), "tenant_id": str(tenant_id), "project_id": str(project_id)}, {"$set": route}
        )
        return await self.get_route(tenant_id, project_id, id)

    async def get_route(self, tenant_id: UUID, project_id: UUID, id: UUID) -> Optional[Route]:
        """Get a route by ID"""
        entity_dict = await self.collection.find_one(
            {"_id": str(id), "tenant_id": str(tenant_id), "project_id": str(project_id)}
        )
        if entity_dict:
            entity = RouteEntity.from_dict(entity_dict)
            return self.mapper.to_domain_model(entity)
        return None

    async def get_routes_by_project(
        self,
        tenant_id: UUID,
        project_id: UUID,
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "created_at",
        sort_order: int = -1,
    ) -> List[Route]:
        """Get routes by project ID"""
        cursor = self.collection.find({"tenant_id": str(tenant_id), "project_id": str(project_id)}).sort(
            sort_by, sort_order
        )
        entities = []
        async for entity_dict in cursor:
            entity = RouteEntity.from_dict(entity_dict)
            entities.append(self.mapper.to_domain_model(entity))
        return entities

    async def delete_route(self, tenant_id: UUID, id: UUID) -> bool:
        """Delete a route by ID"""
        result = await self.collection.delete_one({"_id": str(id), "tenant_id": str(tenant_id)})
        return result.deleted_count > 0

    async def search_routes(self, tenant_id: UUID, query: str) -> List[Route]:
        """Search routes by name or description"""
        filter_dict = {
            "tenant_id": str(tenant_id),
            "$or": [
                {"name": {"$regex": query, "$options": "i"}},
                {"description": {"$regex": query, "$options": "i"}},
            ],
        }
        cursor = self.collection.find(filter_dict).sort("created_at", DESCENDING)
        entities = []
        async for entity_dict in cursor:
            entity = RouteEntity.from_dict(entity_dict)
            entities.append(self.mapper.to_domain_model(entity))
        return entities
