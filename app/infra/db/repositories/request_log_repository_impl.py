from typing import Optional, List, Dict
from uuid import UUID

from motor.motor_asyncio import Async<PERSON><PERSON>otor<PERSON>ollection
from pymongo import DESCENDING

from app.common.utils import get_current_date_time
from app.core.models.request_log import RequestLog, RequestType, RequestStatus
from app.core.outbound.repositories.request_log_repository import RequestLogRepository
from app.infra.db.entities import RequestLogEntity
from app.infra.db.mappers.request_log_mapper import RequestLogMapper
from app.infra.mongo_client.chemstack_db_client import chemstack_db_client


class RequestLogRepositoryImpl(RequestLogRepository):
    """MongoDB implementation of RequestLogRepository"""

    def __init__(self):
        self.mapper = RequestLogMapper()
        self._collection = chemstack_db_client.db.request_logs

    async def create_indexes(self):
        """Create indexes for the request_logs collection"""
        await self._collection.create_index("tenant_id")
        await self._collection.create_index("project_id")

    async def create_log(self, log: RequestLog) -> bool:
        entity = self.mapper.to_entity_model(log)
        result = await self._collection.insert_one(entity.to_dict())
        return result.acknowledged

    async def update_status(self, id: UUID, status: RequestStatus, error_message: str) -> bool:
        updated = await self._collection.update_one(
            {
                "_id": str(id),
                "status": {"$ne": "SUCCESS"}
            },
            {
                "$set": {
                    "status": status,
                    "updated_at": get_current_date_time(),
                    "error_message": error_message
                }
            }
        )
        return updated.modified_count > 0

    async def get_request_ids_by_project_id(self, tenant_id: UUID, project_id: UUID, request_type: RequestType, include_statuses: List[RequestStatus]) -> List[UUID]:
        doc = self._collection.find({
            "tenant_id": str(tenant_id),
            "project_id": str(project_id),
            "request_type": request_type,
            "status": {"$in": [status.value if hasattr(status, 'value') else status for status in include_statuses]}
        }, {"_id": 1})

        request_ids = []
        async for obj in doc:
            request_ids.append(obj["_id"])
        return request_ids

    async def get_all_logs_by_project_id(self, project_id: UUID) -> List[RequestLog]:
        cursor = (
            self._collection.find({"project_id": str(project_id)})
            .sort("created_at", DESCENDING)
        )
        results = []
        async for doc in cursor:
            entity = RequestLogEntity.from_dict(doc)
            results.append(self.mapper.to_domain_model(entity))
        return results

    async def get_project_info_from_request_id(self, request_id: UUID) -> RequestLog:
        doc = await self._collection.find_one(
            {"_id": str(request_id)})
        if doc:
            entity = RequestLogEntity.from_dict(doc)
            return self.mapper.to_domain_model(entity)
        return None
    async def get_project_request_status(self, tenant_id: UUID, request_types: List[RequestType], project_ids: List[UUID] = None) -> Dict:
        match_query = {
            "tenant_id": str(tenant_id),
            "request_type": {"$in": request_types}
        }
        
        if project_ids:
            match_query["project_id"] = {"$in": [str(project_id) for project_id in project_ids]}

        pipeline = [
            {"$match": match_query},
            {
                "$group": {
                    "_id": {
                        "project_id": "$project_id",
                        "request_type": "$request_type"
                    },
                    "statuses": {"$addToSet": "$status"}
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "project_id": "$_id.project_id",
                    "request_type": "$_id.request_type",
                    "status": {
                        "$cond": [
                            {"$in": ["QUEUED", "$statuses"]},
                            "QUEUED",
                            {
                                "$cond": [
                                    {"$in": ["FAILED", "$statuses"]},
                                    "FAILED",
                                    "SUCCESS"
                                ]
                            }
                        ]
                    }
                }
            }
        ]
        cursor = self._collection.aggregate(pipeline)
        results = {}
        async for doc in cursor:
            value_dict = {doc["request_type"]: RequestStatus(doc["status"])}
            if doc["project_id"] in results:
                results[doc["project_id"]].update(value_dict)
            else:
                results[doc["project_id"]] = value_dict

        return results
