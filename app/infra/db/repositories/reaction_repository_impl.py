from typing import List, Optional
from uuid import UUID, uuid4

from motor.motor_asyncio import AsyncIOMotorCollection
from pymongo import DESCENDING

from app.common.utils import get_current_date_time
from app.core.models.reaction import Reaction
from app.core.outbound.repositories.reaction_repository import ReactionRepository
from app.infra.db.entities.reaction_entity import ReactionEntity
from app.infra.db.mappers.reaction_mapper import ReactionMapper
from app.infra.mongo_client.chemstack_db_client import chemstack_db_client


class ReactionRepositoryImpl(ReactionRepository):
    """MongoDB implementation of reaction repository operations"""

    def __init__(self):
        """Initialize reaction repository"""
        self.collection_name = "reactions"
        self.mapper = ReactionMapper()
        self._collection: Optional[AsyncIOMotorCollection] = None

    @property
    def collection(self) -> AsyncIOMotorCollection:
        """Get MongoDB collection"""
        if self._collection is None:
            self._collection = chemstack_db_client.db[self.collection_name]
        return self._collection

    async def create_indexes(self):
        """Create indexes for reactions collection"""
        await self.collection.create_index("tenant_id")
        await self.collection.create_index("project_id")
        await self.collection.create_index("route_id")
        await self.collection.create_index("step_id")
        await self.collection.create_index("created_at")

    async def create_reaction(self, tenant_id: UUID, reaction: Reaction) -> Reaction:
        """Create a new reaction and return the created reaction"""
        reaction.tenant_id = tenant_id
        reaction.id = uuid4()
        reaction.created_at = get_current_date_time()
        reaction.updated_at = reaction.created_at

        entity = self.mapper.to_entity_model(reaction)
        entity_dict = entity.to_dict()
        result = await self.collection.insert_one(entity_dict)
        reaction.id = str(result.inserted_id)
        return reaction

    async def update_reaction(self, tenant_id: UUID, id: UUID, reaction: Reaction) -> Reaction:
        """Update an existing reaction and return the updated reaction"""
        reaction.updated_at = get_current_date_time()
        reaction.tenant_id = tenant_id

        entity = self.mapper.to_entity_model(reaction)
        entity_dict = entity.to_dict()
        await self.collection.update_one({"_id": str(id)}, {"$set": entity_dict})
        reaction.id = id
        return reaction

    async def get_reaction(self, tenant_id: UUID, id: UUID) -> Optional[Reaction]:
        """Get a reaction by ID"""
        entity_dict = await self.collection.find_one({"_id": str(id), "tenant_id": str(tenant_id)})
        if entity_dict:
            entity = ReactionEntity.from_dict(entity_dict)
            return self.mapper.to_domain_model(entity)
        return None

    async def get_reactions_by_step(self, tenant_id: UUID, step_id: UUID) -> List[Reaction]:
        """Get reactions by step ID"""
        cursor = self.collection.find({"tenant_id": str(tenant_id), "step_id": str(step_id)}).sort(
            "created_at", DESCENDING
        )
        entities = []
        async for entity_dict in cursor:
            entity = ReactionEntity.from_dict(entity_dict)
            entities.append(self.mapper.to_domain_model(entity))
        return entities

    async def get_reactions_by_route(self, tenant_id: UUID, route_id: UUID) -> List[Reaction]:
        """Get reactions by route ID"""
        cursor = self.collection.find({"tenant_id": str(tenant_id), "route_id": str(route_id)}).sort(
            "created_at", DESCENDING
        )
        entities = []
        async for entity_dict in cursor:
            entity = ReactionEntity.from_dict(entity_dict)
            entities.append(self.mapper.to_domain_model(entity))
        return entities

    async def get_reactions_by_project(self, tenant_id: UUID, project_id: UUID) -> List[Reaction]:
        """Get reactions by project ID"""
        cursor = self.collection.find({"tenant_id": str(tenant_id), "project_id": str(project_id)}).sort(
            "created_at", DESCENDING
        )
        entities = []
        async for entity_dict in cursor:
            entity = ReactionEntity.from_dict(entity_dict)
            entities.append(self.mapper.to_domain_model(entity))
        return entities

    async def delete_reaction(self, tenant_id: UUID, id: UUID) -> bool:
        """Delete a reaction by ID"""
        result = await self.collection.delete_one({"_id": str(id), "tenant_id": str(tenant_id)})
        return result.deleted_count > 0
