from typing import List, Optional, <PERSON><PERSON>
from uuid import UUID

from pymongo import DESCENDING

from app.common.utils import get_current_date_time
from app.core.models.project import Project, ProjectStatus
from app.core.models.paginated_response import PaginatedResponse
from app.core.outbound.repositories.project_repository import ProjectRepository
from app.infra.db.entities.project_entity import ProjectEntity
from app.infra.db.mappers.project_mapper import ProjectMapper
from app.infra.mongo_client.chemstack_db_client import chemstack_db_client


class ProjectRepositoryImpl(ProjectRepository):
    """Implementation of project repository using MongoDB"""

    def __init__(self):
        self._collection = chemstack_db_client.db.projects
        self._mapper = ProjectMapper()

    async def find_by_id(self, project_id: str) -> Project:
        """Find a project by ID"""
        project_doc = await self._collection.find_one({"_id": project_id})
        if project_doc:
            return self._mapper.to_domain(ProjectEntity(**project_doc))
        return None

    async def save(self, project: Project) -> Project:
        """Save a project"""
        project_entity = self._mapper.to_entity(project)
        await self._collection.replace_one({"_id": project_entity.id}, project_entity.dict(), upsert=True)
        return project

    async def create_indexes(self):
        """Create indexes for projects collection"""
        await self._collection.create_index("name", unique=True)
        await self._collection.create_index("tenant_id")
        await self._collection.create_index("status")
        await self._collection.create_index("created_at")
        await self._collection.create_index("tags")

    async def create_project(self, tenant_id: UUID, project: Project) -> Project:
        """Create a new project and return the created project"""
        project.tenant_id = project.tenant_id
        project.created_at = get_current_date_time()
        project.updated_at = project.created_at
        project.start_date = project.created_at
        project.status = ProjectStatus.IN_PROGRESS

        entity = self._mapper.to_entity_model(project)
        entity_dict = entity.to_dict()
        result = await self._collection.insert_one(entity_dict)

        if not result.inserted_id:
            raise Exception("Failed to create project in database")

        return project

    async def update_project(self, tenant_id: UUID, id: UUID, project: dict) -> Project:
        """Update an existing project and return the updated project"""
        await self._collection.update_one({"tenant_id": str(tenant_id), "_id": str(id)}, {"$set": project})
        return await self.get_project(tenant_id, id)

    async def get_project(self, tenant_id: UUID, id: UUID) -> Optional[Project]:
        """Get a project by ID"""
        entity_dict = await self._collection.find_one({"tenant_id": str(tenant_id), "_id": str(id)})
        if entity_dict:
            entity = ProjectEntity.from_dict(entity_dict)
            return self._mapper.to_domain_model(entity)
        return None

    async def get_projects_by_tenant(
        self, tenant_id: UUID, additional_filters: Optional[dict] = None, skip: int = 0, limit: int = 100
    ) -> PaginatedResponse[Project]:
        """Get projects by tenant with pagination"""
        filter_query = {"tenant_id": str(tenant_id)}
        if additional_filters:
            filter_query.update(additional_filters)
        cursor = self._collection.find(filter_query).sort("created_at", DESCENDING).skip(skip).limit(limit)
        total_count = await self._collection.count_documents(filter_query)
        entities = []
        async for entity_dict in cursor:
            entity = ProjectEntity.from_dict(entity_dict)
            entities.append(self._mapper.to_domain_model(entity))
        return PaginatedResponse(data=entities, total=total_count, skip=skip, limit=limit)

    async def search_projects(self, tenant_id: UUID, query: str, skip: int = 0, limit: int = 100) -> PaginatedResponse[Project]:
        """Search projects by name or compound name"""
        filter_dict = {
            "tenant_id": str(tenant_id),
            "$or": [
                {"name": {"$regex": query, "$options": "i"}},
                {"compound_name": {"$regex": query, "$options": "i"}},
            ],
        }
        cursor = self._collection.find(filter_dict).sort("created_at", DESCENDING).skip(skip).limit(limit)
        total_count = await self._collection.count_documents(filter_dict)
        entities = []
        async for entity_dict in cursor:
            entity = ProjectEntity.from_dict(entity_dict)
            entities.append(self._mapper.to_domain_model(entity))
        
        return PaginatedResponse(data=entities, total=total_count, skip=skip, limit=limit)

    async def delete_project(self, tenant_id: UUID, project_id: UUID) -> bool:
        """Delete a project by ID"""
        result = await self._collection.delete_one({"tenant_id": str(tenant_id), "project_id": str(project_id)})
        return result.deleted_count > 0
