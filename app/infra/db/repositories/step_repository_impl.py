from typing import List, Optional
from uuid import UUID, uuid4

from motor.motor_asyncio import AsyncIOMotorCollection

from app.common.utils import get_current_date_time
from app.core.models.step import Step
from app.core.outbound.repositories.step_repository import StepRepository
from app.infra.db.entities.step_entity import StepEntity
from app.infra.db.mappers.step_mapper import Step<PERSON>apper
from app.infra.mongo_client.chemstack_db_client import chemstack_db_client


class StepRepositoryImpl(StepRepository):
    """MongoDB implementation of step repository operations"""

    def __init__(self):
        """Initialize step repository"""
        self.collection_name = "steps"
        self.mapper = StepMapper()
        self._collection: Optional[AsyncIOMotorCollection] = None

    @property
    def collection(self) -> AsyncIOMotorCollection:
        """Get MongoDB collection"""
        if self._collection is None:
            self._collection = chemstack_db_client.db[self.collection_name]
        return self._collection

    async def create_indexes(self):
        """Create indexes for steps collection"""
        await self.collection.create_index("tenant_id")
        await self.collection.create_index("project_id")
        await self.collection.create_index("route_id")
        await self.collection.create_index("step_number")
        await self.collection.create_index("created_at")

    async def create_step(self, tenant_id: UUID, project_id: UUID, route_id: UUID, step: Step) -> Step:
        """Create a new step and return the created step"""
        step.tenant_id = tenant_id
        step.project_id = project_id
        step.route_id = route_id
        step.id = uuid4()
        step.created_at = get_current_date_time()
        step.updated_at = step.created_at

        entity = self.mapper.to_entity_model(step)
        entity_dict = entity.to_dict()
        result = await self.collection.insert_one(entity_dict)
        step.id = str(result.inserted_id)
        return step

    async def update_step(self, tenant_id: UUID, id: UUID, step: Step) -> Step:
        """Update an existing step and return the updated step"""
        step.updated_at = get_current_date_time()
        step.tenant_id = tenant_id

        entity = self.mapper.to_entity_model(step)
        entity_dict = entity.to_dict()
        await self.collection.update_one({"_id": str(id)}, {"$set": entity_dict})
        step.id = id
        return step

    async def get_step_by_id(self, tenant_id: UUID, project_id: UUID, route_id: UUID, step_id: UUID) -> Optional[Step]:
        """Get a step by ID"""
        entity_dict = await self.collection.find_one(
            {"_id": str(step_id), "tenant_id": str(tenant_id), "project_id": str(project_id), "route_id": str(route_id)}
        )
        if entity_dict:
            entity = StepEntity.from_dict(entity_dict)
            return self.mapper.to_domain_model(entity)
        return None

    async def get_steps_by_route(
        self,
        tenant_id: UUID,
        project_id: UUID,
        route_id: UUID,
        skip: int = 0,
        limit: int = 100,
        sort_by: str = "step_number",
        sort_order: int = 1,
    ) -> List[Step]:
        """Get steps by route ID"""
        cursor = self.collection.find(
            {"tenant_id": str(tenant_id), "project_id": str(project_id), "route_id": str(route_id)}
        ).sort(sort_by, sort_order)
        entities = []
        async for entity_dict in cursor:
            entity = StepEntity.from_dict(entity_dict)
            entities.append(self.mapper.to_domain_model(entity))
        return entities
