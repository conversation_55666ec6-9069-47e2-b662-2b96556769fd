from uuid import UUID
from typing import List

from app.core.models.retro_route_review import RetroRouteReview
from app.core.outbound.repositories.retro_route_review_repository import RetroRouteReviewRepository
from app.infra.mongo_client.chemstack_db_client import chemstack_db_client
from app.infra.db.entities.retro_route_review_entity import RetroRouteReviewEntity
from app.infra.db.mappers.retro_route_review_mapper import RetroRouteReviewMapper
from app.infra.mongo_client.chemstack_db_client import chemstack_db_client


class RetroRouteReviewRepositoryImpl(RetroRouteReviewRepository):
    def __init__(self):
        self._mapper = RetroRouteReviewMapper()
        self._collection = chemstack_db_client.db.retro_route_reviews

    async def create(self, tenant_id: UUID, retro_route_review: RetroRouteReview) -> RetroRouteReview:
        """
        Create a new retro route review
        """
        entity = self._mapper.to_entity_model(retro_route_review)
        await self._collection.insert_one(entity.to_dict())
        return retro_route_review

    async def update(self, tenant_id: UUID, id: UUID, retro_route_review: RetroRouteReview) -> RetroRouteReview:
        """
        Update a retro route review
        """
        entity = self._mapper.to_entity_model(retro_route_review)
        response = await self._collection.update_one({"_id": str(id)}, {"$set": entity.to_dict()})
        if response.modified_count == 0:
            raise Exception("Failed to update retro route review")
        return retro_route_review

    async def get_by_project_id(self, tenant_id: UUID, project_id: UUID) -> List[RetroRouteReview]:
        """
        Get all retro route reviews for a project
        """
        query = {"tenant_id": str(tenant_id), "project_id": str(project_id)}
        cursor = self._collection.find(query).sort("created_at", -1)
        entities = []
        async for entity_dict in cursor:
            entity = RetroRouteReviewEntity.from_dict(entity_dict)
            entities.append(self._mapper.to_domain_model(entity))
        return entities

    async def get_by_retro_route_id(self, tenant_id: UUID, project_id: UUID, retro_route_id: UUID) -> RetroRouteReview:
        """
        Get a retro route review by retro route id
        """
        query = {"tenant_id": str(tenant_id), "project_id": str(project_id), "retro_route_id": str(retro_route_id)}
        entity_dict = await self._collection.find_one(query)
        if entity_dict:
            entity = RetroRouteReviewEntity.from_dict(entity_dict)
            return self._mapper.to_domain_model(entity)
        return None

    async def get_by_retro_route_ids(
        self, tenant_id: UUID, project_id: UUID, retro_route_ids: List[UUID]
    ) -> List[RetroRouteReview]:
        """
        Get a retro route review by retro route ids
        """
        query = {
            "tenant_id": str(tenant_id),
            "project_id": str(project_id),
            "retro_route_id": {"$in": [str(rid) for rid in retro_route_ids]},
        }
        cursor = self._collection.find(query).sort("created_at", -1)
        entities = []
        async for entity_dict in cursor:
            entity = RetroRouteReviewEntity.from_dict(entity_dict)
            entities.append(self._mapper.to_domain_model(entity))
        return entities

    async def get_retro_route_ids_by_status(
        self, tenant_id: UUID, project_id: UUID, status: str
    ) -> List[UUID]:
        """
        Get retro route IDs that have a specific status
        """
        query = {
            "tenant_id": str(tenant_id),
            "project_id": str(project_id),
            "status": status,
        }
        cursor = self._collection.find(query, {"retro_route_id": 1})
        retro_route_ids = []
        async for doc in cursor:
            if doc.get("retro_route_id"):
                retro_route_ids.append(UUID(doc["retro_route_id"]))
        return retro_route_ids
