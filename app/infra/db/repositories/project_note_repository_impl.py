from typing import List, Optional, Dict, Any
from datetime import datetime, UTC
from uuid import UUID, uuid4
from app.api.schemas.project_note import NoteType, Priority
from app.core.models.project_note import ProjectNote
from app.core.outbound.repositories.project_note_repository import ProjectNoteRepository
from motor.motor_asyncio import AsyncIOMotorCollection

from app.infra.db.mappers.project_note_mapper import ProjectNoteMapper
from app.infra.mongo_client.chemstack_db_client import chemstack_db_client
from app.core.outbound.repositories.project_note_repository import ProjectNoteRepository


class ProjectNoteRepositoryImpl(ProjectNoteRepository):
    """Implementation of the ProjectNoteRepository"""

    def __init__(self):
        """Initialize project note repository"""
        self.collection_name = "project_notes"
        self._collection: Optional[AsyncIOMotorCollection] = None
        self.mapper = ProjectNoteMapper()

    @property
    def collection(self) -> AsyncIOMotorCollection:
        """Get MongoDB collection"""
        if self._collection is None:
            self._collection = chemstack_db_client.db[self.collection_name]
        return self._collection

    async def create_indexes(self):
        """Create indexes for research_workspace collection"""
        await self.collection.create_index("tenant_id")
        await self.collection.create_index("project_id")

    async def get_project_notes(
        self, tenant_id: UUID, project_id: UUID, sort_by: Optional[str] = None, note_type: Optional[str] = None
    ) -> List[ProjectNote]:
        """
        Retrieve all notes for a project with sorting and filtering.
        """
        # Base query
        query = {"tenant_id": str(tenant_id), "project_id": str(project_id), "deleted": {"$ne": True}}   
        # Add note type filter
        if note_type:
            query["note_type"] = note_type

        # Get all notes first
        cursor = self.collection.find(query)
        notes = await cursor.to_list(length=None)

        # Define the order for types for stable sorting
        type_order = {NoteType.PATENT_CLAIMS: 1, NoteType.PATENT_REACTIONS: 2, NoteType.PATHWAYS: 3, NoteType.QUICK_NOTES: 4}

        # Apply sorting
        if sort_by == "priority":
            # Stable sort by: 1. Priority (desc), 2. Type (asc), 3. Date (desc)
            notes.sort(key=lambda x: x.get("created_at", datetime.min), reverse=True)
            notes.sort(key=lambda x: type_order.get(x.get("note_type", NoteType.QUICK_NOTES), 5))
            priority_order = {Priority.HIGH: 3, Priority.MEDIUM: 2, Priority.LOW: 1}
            notes.sort(key=lambda x: priority_order.get(x.get("priority", Priority.LOW), 0), reverse=True)

        elif sort_by == "type":
            # Stable sort by: 1. Type (asc), 2. Date (desc)
            notes.sort(key=lambda x: x.get("created_at", datetime.min), reverse=True)
            notes.sort(key=lambda x: type_order.get(x.get("note_type", NoteType.QUICK_NOTES), 5))

        else:  # Default to sorting by date if sort_by is 'date' or not provided
            notes.sort(key=lambda x: x.get("created_at", datetime.min), reverse=True)

        return [note for note in notes]

    async def create_project_note(
        self, tenant_id: UUID, project_id: UUID, user_id: UUID, note: ProjectNote
    ) -> ProjectNote:
        """
        Create a new project note.
        """
        entity_dict = self.mapper.to_entity_model(note)
        result = await self.collection.insert_one(entity_dict.to_dict())
        if not result.inserted_id:
            raise Exception("Failed to create project note in database")
        note.id = result.inserted_id
        return note

    async def update_project_note(
        self, tenant_id: UUID, project_id: UUID, note_id: UUID, user_id: UUID, note: ProjectNote
    ) -> ProjectNote:
        """
        Update an existing project note.
        """
        now = datetime.utcnow()
        update_data = {
            "note": note.note,
            "updated_at": now
        }
        query = {
            "_id": str(note_id),
            "tenant_id": str(tenant_id),
            "project_id": str(project_id),
            "deleted": {"$ne": True},
            "created_by": str(user_id)
        }
        result = await self.collection.update_one(query, {"$set": update_data})
        if result.modified_count == 0:
            raise ValueError("Note not found or no changes made")

        updated_note = await self.collection.find_one(query)
        return updated_note

    async def delete_project_note(self, tenant_id: UUID, project_id: UUID, user_id: UUID, note_id: UUID) -> bool:
        """
        Soft delete a workspace note by setting 'deleted' and 'deleted_at' fields. Only the creator can delete.
        """
        # 1. Fetch the note to be deleted
        note = await self.collection.find_one({
            "_id": str(note_id),
            "tenant_id": str(tenant_id),
            "project_id": str(project_id),
            "created_by": str(user_id),
            "deleted": {"$ne": True}
        })
        if not note:
            return False

        # 3. Soft delete: set 'deleted' and 'deleted_at'
        result = await self.collection.update_one(
            {
                "_id": str(note_id),
                "tenant_id": str(tenant_id),
                "project_id": str(project_id),
                "created_by": str(user_id)
            },
            {"$set": {"deleted": True, "deleted_at": datetime.utcnow(), "deleted_by": str(user_id)}}
        )
        return result.modified_count == 1
    
    async def get_quick_notes(self, tenant_id: UUID, project_id: UUID) -> List[ProjectNote]:
        cursor = self.collection.find({
            "tenant_id": str(tenant_id),
            "project_id": str(project_id),
            "note_type": NoteType.QUICK_NOTES,
            "deleted": {"$ne": True}
        }).sort("created_at", 1)
        return await cursor.to_list(length=None)
    
    async def get_shortlisted_claim_count(self, tenant_id: UUID, project_id: UUID, note_type: NoteType) -> int:
        """Returns the count for a specific note type"""
        pipeline = [
            {
                "$match": {
                    "tenant_id": str(tenant_id),
                    "project_id": str(project_id),
                    "note_type": note_type.value,
                    "deleted": {"$ne": True}
                }
            },
            {
                "$group": {
                    "_id": {
                        "note_type_id": "$note_type_id",
                        "name": "$name"
                    }
                }
            },
            {
                "$count": "count"
            }
        ]
        result = await self.collection.aggregate(pipeline).to_list(length=1)
        return result[0]["count"] if result else 0
