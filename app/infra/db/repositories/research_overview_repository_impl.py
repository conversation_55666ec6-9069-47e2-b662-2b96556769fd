from datetime import datetime
from uuid import UUID
from app.core.models.project import Project
from app.core.outbound.repositories.research_overview_repository import ResearchOverviewRepository
from app.infra.mongo_client.chemstack_db_client import chemstack_db_client
from app.infra.db.entities.project_entity import ProjectEntity

class ResearchOverviewRepositoryImpl(ResearchOverviewRepository):
    def __init__(self):
        self._projects_collection = chemstack_db_client.db.projects
        self._project_notes_collection = chemstack_db_client.db.project_notes
        self._retro_route_reviews_collection = chemstack_db_client.db.retro_route_reviews

    async def create_indexes(self):
        # Projects collection indexes
        await self._projects_collection.create_index("status")
        await self._projects_collection.create_index("created_at")
        await self._projects_collection.create_index("tenant_id")

        # Project Notes collection indexes
        await self._project_notes_collection.create_index("note_type")
        await self._project_notes_collection.create_index("created_at")
        await self._project_notes_collection.create_index("tenant_id")
        await self._project_notes_collection.create_index("created_by")
        await self._project_notes_collection.create_index("project_id")
        await self._project_notes_collection.create_index("user_name")  # Optional

        # Retro Route Reviews collection indexes
        await self._retro_route_reviews_collection.create_index("status")
        await self._retro_route_reviews_collection.create_index("created_at")
        await self._retro_route_reviews_collection.create_index("tenant_id")
        await self._retro_route_reviews_collection.create_index("created_by")
        await self._retro_route_reviews_collection.create_index("project_id")

        #compound_indexes
        await self._project_notes_collection.create_index([("tenant_id", 1), ("note_type", 1), ("created_at", 1)])
        await self._retro_route_reviews_collection.create_index([("tenant_id", 1), ("status", 1), ("created_at", 1)])
        await self._projects_collection.create_index([("tenant_id", 1), ("status", 1)])
        await self._projects_collection.create_index([("_id", 1), ("tenant_id", 1)])

    async def get_weekly_patent_count(self, tenant_id: UUID, start_date: datetime, end_date: datetime) -> int:
        return await self._project_notes_collection.count_documents({
            "tenant_id": str(tenant_id),
            "note_type": "patent_claims",
            "created_at": {"$gte": start_date, "$lte": end_date}
        })

    async def get_weekly_pathways_count(self, tenant_id: UUID, start_date: datetime, end_date: datetime) -> int:
        return await self._retro_route_reviews_collection.count_documents({
            "tenant_id": str(tenant_id),
            "status": "shortlisted",
            "created_at": {"$gte": start_date, "$lte": end_date}
        })

    async def get_total_projects(self, tenant_id: UUID) -> int:
        return await self._projects_collection.count_documents({"tenant_id": str(tenant_id)})

    async def get_ongoing_projects(self, tenant_id: UUID) -> int:
        return await self._projects_collection.count_documents({
            "tenant_id": str(tenant_id),
            "status": "in_progress"
        })

    async def get_top_pathway_contributor_raw(self, tenant_id: UUID, last_24: datetime) -> dict:
        pipeline = [
            {"$match": {"tenant_id": str(tenant_id), "status": "shortlisted", "created_at": {"$gte": last_24}}},
            {"$group": {"_id": {"created_by": "$created_by", "project_id": "$project_id"}, "count": {"$sum": 1}}},
            {"$sort": {"count": -1}},
            {"$limit": 1}
        ]
        cursor = self._project_notes_collection.aggregate(pipeline)
        try:
            top_contributor = await anext(cursor)
            return top_contributor
        except StopAsyncIteration:
            return None

    async def get_top_patent_contributor_raw(self, tenant_id: UUID, last_24: datetime) -> dict:
        pipeline = [
            {"$match": {"tenant_id": str(tenant_id), "note_type": "patent_claims", "created_at": {"$gte": last_24}}},
            {"$group": {"_id": {"created_by": "$created_by", "project_id": "$project_id", "user_name": "$user_name"}, "count": {"$sum": 1}}},
            {"$sort": {"count": -1}},
            {"$limit": 1}
        ]
        cursor = self._project_notes_collection.aggregate(pipeline)
        try:
            top_contributor = await anext(cursor)
            return top_contributor
        except StopAsyncIteration:
            return None

    async def get_project_by_id(self, project_id: str, tenant_id: UUID) -> ProjectEntity:
        entity_dict = await self._projects_collection.find_one({
            "_id": project_id,
            "tenant_id": str(tenant_id)
        })
        return ProjectEntity.from_dict(entity_dict) if entity_dict else None
