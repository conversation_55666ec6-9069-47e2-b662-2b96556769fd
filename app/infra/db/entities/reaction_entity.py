from typing import Optional, List, Dict, Any
from datetime import datetime, UTC
from uuid import UUID
from ...common.db.base_entity import BaseEntity
from app.core.models.reaction import Identifier, Input, Setup, Conditions


class ReactionEntity(BaseEntity):
    """Reaction entity for MongoDB"""

    def __init__(
        self,
        _id: Optional[UUID] = None,
        tenant_id: UUID = None,
        project_id: UUID = None,
        route_id: UUID = None,
        step_id: UUID = None,
        identifiers: List[Identifier] = None,
        inputs: List[Input] = None,
        setup: Optional[Setup] = None,
        conditions: Optional[Conditions] = None,
        created_by: Optional[UUID] = None,
        updated_by: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ):
        super().__init__(_id=_id, created_at=created_at, updated_at=updated_at)
        self.tenant_id = tenant_id
        self.project_id = project_id
        self.route_id = route_id
        self.step_id = step_id
        self.identifiers = identifiers or []
        self.inputs = inputs or []
        self.setup = setup
        self.conditions = conditions
        self.created_by = created_by
        self.updated_by = updated_by

    def to_dict(self) -> dict:
        """Convert entity to dictionary"""
        data = super().to_dict()
        data.update(
            {
                "tenant_id": str(self.tenant_id),
                "project_id": str(self.project_id),
                "route_id": str(self.route_id),
                "step_id": str(self.step_id),
                "identifiers": [identifier.model_dump() for identifier in self.identifiers],
                "inputs": [input.model_dump() for input in self.inputs],
                "setup": self.setup.model_dump() if self.setup else None,
                "conditions": self.conditions.model_dump() if self.conditions else None,
                "created_by": str(self.created_by) if self.created_by else None,
                "updated_by": str(self.updated_by) if self.updated_by else None,
            }
        )
        return data

    @classmethod
    def from_dict(cls, data: dict) -> "ReactionEntity":
        """Create entity from dictionary"""
        return cls(
            _id=UUID(data["_id"]) if data.get("_id") else None,
            tenant_id=UUID(data["tenant_id"]),
            project_id=UUID(data["project_id"]),
            route_id=UUID(data["route_id"]),
            step_id=UUID(data["step_id"]),
            identifiers=[Identifier.model_validate(i) for i in data.get("identifiers", [])],
            inputs=[Input.model_validate(i) for i in data.get("inputs", [])],
            setup=Setup.model_validate(data["setup"]) if data.get("setup") else None,
            conditions=Conditions.model_validate(data["conditions"]) if data.get("conditions") else None,
            created_by=UUID(data["created_by"]) if data.get("created_by") else None,
            updated_by=UUID(data["updated_by"]) if data.get("updated_by") else None,
            created_at=data.get("created_at"),
            updated_at=data.get("updated_at"),
        )
