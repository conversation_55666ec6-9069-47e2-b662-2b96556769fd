from typing import Optional
from datetime import datetime
from uuid import UUID
from ...common.db.base_entity import BaseEntity
from app.core.models.retro_route_review import RetroRouteReviewStatus


class RetroRouteReviewEntity(BaseEntity):
    """Project entity for MongoDB"""

    def __init__(
        self,
        _id: UUID,
        tenant_id: UUID,
        project_id: UUID,
        retro_route_id: UUID,
        status: RetroRouteReviewStatus,
        review_text: str,
        created_by: Optional[UUID] = None,
        updated_by: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ):
        super().__init__(_id=_id, created_at=created_at, updated_at=updated_at)
        self.tenant_id = tenant_id
        self.project_id = project_id
        self.retro_route_id = retro_route_id
        self.status = status
        self.review_text = review_text
        self.created_by = created_by
        self.updated_by = updated_by

    def to_dict(self) -> dict:
        """Convert entity to dictionary"""
        data = super().to_dict()
        data.update(
            {
                "tenant_id": str(self.tenant_id),
                "project_id": str(self.project_id),
                "retro_route_id": str(self.retro_route_id),
                "status": self.status,
                "review_text": self.review_text,
                "created_by": str(self.created_by) if self.created_by else None,
                "updated_by": str(self.updated_by) if self.updated_by else None,
                "created_at": self.created_at,
                "updated_at": self.updated_at,
            }
        )
        return data

    @classmethod
    def from_dict(cls, data: dict) -> "RetroRouteReviewEntity":
        """Create entity from dictionary"""
        return cls(
            _id=UUID(data["_id"]) if data.get("_id") else None,
            tenant_id=UUID(data["tenant_id"]),
            project_id=UUID(data["project_id"]),
            retro_route_id=UUID(data["retro_route_id"]),
            status=data["status"],
            review_text=data["review_text"],
            created_by=UUID(data["created_by"]) if data.get("created_by") else None,
            updated_by=UUID(data["updated_by"]) if data.get("updated_by") else None,
            created_at=data["created_at"],
            updated_at=data["updated_at"],
        )
