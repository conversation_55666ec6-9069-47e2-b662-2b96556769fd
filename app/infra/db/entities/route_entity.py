from typing import Optional, List, Dict
from datetime import datetime, UTC
from uuid import UUID
from ...common.db.base_entity import BaseEntity


class Attachment:
    """Schema for route attachments"""

    def __init__(
        self,
        type: str,
        url: str,
        name: str,
        size: Optional[int] = None,
        mime_type: Optional[str] = None,
    ):
        self.type = type
        self.url = url
        self.name = name
        self.size = size
        self.mime_type = mime_type

    def to_dict(self) -> dict:
        """Convert attachment to dictionary"""
        return {
            "type": self.type,
            "url": self.url,
            "name": self.name,
            "size": self.size,
            "mime_type": self.mime_type,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "Attachment":
        """Create attachment from dictionary"""
        return cls(
            type=data["type"],
            url=data["url"],
            name=data["name"],
            size=data.get("size"),
            mime_type=data.get("mime_type"),
        )


class RouteEntity(BaseEntity):
    """Route entity for MongoDB"""

    def __init__(
        self,
        _id: Optional[UUID] = None,
        tenant_id: UUID = None,
        project_id: UUID = None,
        name: str = None,
        description: Optional[str] = None,
        team: Optional[str] = None,
        status: str = None,
        priority: Optional[str] = None,
        steps_count: int = 0,
        route_cost_in_usd: Optional[float] = None,
        certainty: Optional[float] = None,
        score: Optional[float] = None,
        rank: Optional[int] = None,
        yield_value: Optional[float] = None,
        approved_by: Optional[UUID] = None,
        approved_at: Optional[datetime] = None,
        tags: List[str] = None,
        attachments: List[Attachment] = None,
        created_by: Optional[UUID] = None,
        updated_by: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ):
        super().__init__(_id=_id, created_at=created_at, updated_at=updated_at)
        self.tenant_id = tenant_id
        self.project_id = project_id
        self.name = name
        self.description = description
        self.team = team
        self.status = status
        self.priority = priority
        self.steps_count = steps_count
        self.route_cost_in_usd = route_cost_in_usd
        self.certainty = certainty
        self.score = score
        self.rank = rank
        self.yield_value = yield_value
        self.approved_by = approved_by
        self.approved_at = approved_at
        self.tags = tags or []
        self.attachments = attachments or []
        self.created_by = created_by
        self.updated_by = updated_by

    def to_dict(self) -> dict:
        """Convert entity to dictionary"""
        data = super().to_dict()
        data.update(
            {
                "tenant_id": str(self.tenant_id),
                "project_id": str(self.project_id),
                "name": self.name,
                "description": self.description,
                "team": self.team,
                "status": self.status,
                "priority": self.priority,
                "steps_count": self.steps_count,
                "route_cost_in_usd": self.route_cost_in_usd,
                "certainty": self.certainty,
                "score": self.score,
                "rank": self.rank,
                "yield": self.yield_value,
                "approved_by": str(self.approved_by) if self.approved_by else None,
                "approved_at": self.approved_at,
                "tags": self.tags,
                "attachments": [attachment.to_dict() for attachment in self.attachments],
                "created_by": str(self.created_by) if self.created_by else None,
                "updated_by": str(self.updated_by) if self.updated_by else None,
            }
        )
        return data

    @classmethod
    def from_dict(cls, data: dict) -> "RouteEntity":
        """Create entity from dictionary"""
        return cls(
            _id=UUID(data["_id"]) if data.get("_id") else None,
            tenant_id=UUID(data["tenant_id"]),
            project_id=UUID(data["project_id"]),
            name=data["name"],
            description=data.get("description"),
            team=data.get("team"),
            status=data["status"],
            priority=data.get("priority"),
            steps_count=data.get("steps_count", 0),
            route_cost_in_usd=data.get("route_cost_in_usd"),
            certainty=data.get("certainty"),
            score=data.get("score"),
            rank=data.get("rank"),
            yield_value=data.get("yield"),
            approved_by=UUID(data["approved_by"]) if data.get("approved_by") else None,
            approved_at=data.get("approved_at"),
            tags=data.get("tags", []),
            attachments=[Attachment.from_dict(a) for a in data.get("attachments", [])],
            created_by=UUID(data["created_by"]) if data.get("created_by") else None,
            updated_by=UUID(data["updated_by"]) if data.get("updated_by") else None,
            created_at=data.get("created_at"),
            updated_at=data.get("updated_at"),
        )
