from typing import Optional
from datetime import datetime, UTC
from uuid import UUID
from ...common.db.base_entity import BaseEntity


class StepEntity(BaseEntity):
    """Step entity for MongoDB"""

    def __init__(
        self,
        _id: Optional[UUID] = None,
        tenant_id: UUID = None,
        project_id: UUID = None,
        route_id: UUID = None,
        name: str = None,
        description: Optional[str] = None,
        step_number: int = None,
        rxn_string: str = None,
        rxn_type: str = None,
        rxn_class: Optional[str] = None,
        rxn_superclass: Optional[str] = None,
        certainty: Optional[float] = None,
        predicted_yield: Optional[float] = None,
        experimental_yield: Optional[float] = None,
        notes: Optional[str] = None,
        created_by: Optional[UUID] = None,
        updated_by: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ):
        super().__init__(_id=_id, created_at=created_at, updated_at=updated_at)
        self.tenant_id = tenant_id
        self.project_id = project_id
        self.route_id = route_id
        self.name = name
        self.description = description
        self.step_number = step_number
        self.rxn_string = rxn_string
        self.rxn_type = rxn_type
        self.rxn_class = rxn_class
        self.rxn_superclass = rxn_superclass
        self.certainty = certainty
        self.predicted_yield = predicted_yield
        self.experimental_yield = experimental_yield
        self.notes = notes
        self.created_by = created_by
        self.updated_by = updated_by

    def to_dict(self) -> dict:
        """Convert entity to dictionary"""
        data = super().to_dict()
        data.update(
            {
                "tenant_id": str(self.tenant_id),
                "project_id": str(self.project_id),
                "route_id": str(self.route_id),
                "name": self.name,
                "description": self.description,
                "step_number": self.step_number,
                "rxn_string": self.rxn_string,
                "rxn_type": self.rxn_type,
                "rxn_class": self.rxn_class,
                "rxn_superclass": self.rxn_superclass,
                "certainty": self.certainty,
                "predicted_yield": self.predicted_yield,
                "experimental_yield": self.experimental_yield,
                "notes": self.notes,
                "created_by": str(self.created_by) if self.created_by else None,
                "updated_by": str(self.updated_by) if self.updated_by else None,
            }
        )
        return data

    @classmethod
    def from_dict(cls, data: dict) -> "StepEntity":
        """Create entity from dictionary"""
        return cls(
            _id=UUID(data["_id"]) if data.get("_id") else None,
            tenant_id=UUID(data["tenant_id"]),
            project_id=UUID(data["project_id"]),
            route_id=UUID(data["route_id"]),
            name=data["name"],
            description=data.get("description"),
            step_number=data["step_number"],
            rxn_string=data["rxn_string"],
            rxn_type=data["rxn_type"],
            rxn_class=data.get("rxn_class"),
            rxn_superclass=data.get("rxn_superclass"),
            certainty=data.get("certainty"),
            predicted_yield=data.get("predicted_yield"),
            experimental_yield=data.get("experimental_yield"),
            notes=data.get("notes"),
            created_by=UUID(data["created_by"]) if data.get("created_by") else None,
            updated_by=UUID(data["updated_by"]) if data.get("updated_by") else None,
            created_at=data.get("created_at"),
            updated_at=data.get("updated_at"),
        )
