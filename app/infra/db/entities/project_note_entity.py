from datetime import datetime
from typing import List, Optional
from uuid import UUID
from app.infra.common.db.base_entity import BaseEntity


class ProjectNoteEntity(BaseEntity):
    """Project Note entity for MongoDB"""

    def __init__(
        self,
        _id: Optional[UUID] = None,
        tenant_id: UUID = None,
        project_id: UUID = None,
        name: Optional[str] = None,
        note: Optional[str] = None,
        description: Optional[str] = None,
        note_type: Optional[str] = None,
        note_type_id: Optional[str] = None,
        priority: Optional[str] = None,
        created_by: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
        user_name: str = None,
    ):
        super().__init__(_id=_id, created_at=created_at, updated_at=updated_at)
        self.note_type = note_type
        self.note_type_id = note_type_id
        self.priority = priority
        self.description = description
        self.name = name
        self.tenant_id = tenant_id
        self.project_id = project_id
        self.note = note
        self.created_by = created_by
        self.user_name = user_name

    def to_dict(self) -> dict:
        """Convert entity to dictionary"""
        data = super().to_dict()
        data.update(
            {
                "tenant_id": str(self.tenant_id),
                "project_id": str(self.project_id),
                "name": self.name,
                "note": self.note,
                "description": self.description,
                "note_type": self.note_type,
                "note_type_id": self.note_type_id,
                "priority": self.priority,
                "created_by": str(self.created_by) if self.created_by else None,
                "created_at": self.created_at,
                "updated_at": self.updated_at,
                "user_name": self.user_name,
            }
        )
        return data

    @classmethod
    def from_dict(cls, data: dict) -> "ProjectNoteEntity":
        """Create entity from dictionary"""
        return cls(
            _id=UUID(data["_id"]) if data.get("_id") else None,
            tenant_id=UUID(data["tenant_id"]),
            project_id=UUID(data["project_id"]),
            name=data.get("name", ""),
            description=data.get("description", ""),
            note=data.get("note", ""),
            note_type=data.get("note_type", ""),
            note_type_id=data.get("note_type_id", ""),
            priority=data.get("priority", ""),
            created_by=UUID(data["created_by"]) if data.get("created_by") else None,
            created_at=data.get("created_at"),
            updated_at=data.get("updated_at"),
            user_name=data.get("user_name", ""),
        )
