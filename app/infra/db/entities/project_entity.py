from typing import Optional, List
from datetime import datetime, UTC
from uuid import UUID
from ...common.db.base_entity import BaseEntity


class ProjectEntity(BaseEntity):
    """Project entity for MongoDB"""

    def __init__(
        self,
        _id: UUID,
        tenant_id: UUID,
        compound_name: str,
        canonical_smiles: str,
        status: str,
        molecule_image_url: str,
        shortlisted_routes_count: Optional[int] = 0,
        owner: Optional[UUID] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        end_state: Optional[str] = None,
        approved_by: Optional[UUID] = None,
        tags: Optional[List[str]] = None,
        description: Optional[str] = None,
        collaborators: Optional[dict] = None,
        created_by: Optional[UUID] = None,
        updated_by: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ):
        super().__init__(_id=_id, created_at=created_at, updated_at=updated_at)
        self.tenant_id = tenant_id
        self.owner = owner
        self.compound_name = compound_name
        self.canonical_smiles = canonical_smiles
        self.molecule_image_url = molecule_image_url
        self.shortlisted_routes_count = shortlisted_routes_count
        self.start_date = start_date
        self.status = status
        self.end_date = end_date
        self.end_state = end_state
        self.approved_by = approved_by
        self.tags = tags or []
        self.collaborators = collaborators
        self.description = description
        self.created_by = created_by
        self.updated_by = updated_by

    def to_dict(self) -> dict:
        """Convert entity to dictionary"""
        data = super().to_dict()
        data.update(
            {
                "tenant_id": str(self.tenant_id),
                "owner": str(self.owner) if self.owner else None,
                "compound_name": self.compound_name,
                "canonical_smiles": self.canonical_smiles,
                "molecule_image_url": self.molecule_image_url,
                "shortlisted_routes_count": self.shortlisted_routes_count,
                "start_date": self.start_date,
                "status": self.status,
                "end_date": self.end_date,
                "end_state": self.end_state,
                "approved_by": str(self.approved_by) if self.approved_by else None,
                "tags": self.tags,
                "description": self.description,
                "collaborators": self.collaborators,
                "created_by": str(self.created_by) if self.created_by else None,
                "updated_by": str(self.updated_by) if self.updated_by else None,
            }
        )
        return data

    @classmethod
    def from_dict(cls, data: dict) -> "ProjectEntity":
        """Create entity from dictionary"""
        return cls(
            _id=UUID(data["_id"]) if data.get("_id") else None,
            tenant_id=UUID(data["tenant_id"]),
            owner=data["owner"] if data.get("owner") else None,
            compound_name=data["compound_name"],
            canonical_smiles=data["canonical_smiles"],
            molecule_image_url=data["molecule_image_url"],
            shortlisted_routes_count=data.get("shortlisted_routes_count", 0),
            start_date=data.get("start_date"),
            status=data["status"],
            end_date=data.get("end_date"),
            end_state=data.get("end_state"),
            approved_by=UUID(data["approved_by"]) if data.get("approved_by") else None,
            tags=data.get("tags", []),
            description=data.get("description"),
            collaborators=data.get("collaborators", {}),
            created_by=UUID(data["created_by"]) if data.get("created_by") else None,
            updated_by=UUID(data["updated_by"]) if data.get("updated_by") else None,
            created_at=data.get("created_at"),
            updated_at=data.get("updated_at"),
        )
