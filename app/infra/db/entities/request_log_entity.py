from datetime import datetime
from typing import Optional
from uuid import UUID

from ...common.db.base_entity import BaseEntity


class RequestLogEntity(BaseEntity):
    """Request Log entity for MongoDB"""

    def __init__(
        self,
        _id: UUID,
        project_id: UUID,
        tenant_id: UUID,
        user_id: UUID,
        request_type: str,
        status: str,
        created_at: datetime,
        updated_at: datetime,
        error_message: Optional[str] = None
    ):
        super().__init__(_id=_id, created_at=created_at, updated_at=updated_at)
        self.project_id = project_id
        self.tenant_id = tenant_id
        self.user_id = user_id
        self.request_type = request_type
        self.status = status
        self.error_message = error_message

    def to_dict(self) -> dict:
        data = super().to_dict()
        data.update({
            "project_id": str(self.project_id),
            "tenant_id": str(self.tenant_id),
            "user_id": str(self.user_id),
            "request_type": self.request_type,
            "status": self.status,
            "error_message": self.error_message if self.error_message else None
        })
        return data

    @classmethod
    def from_dict(cls, data: dict) -> "RequestLogEntity":
        """Create entity from dictionary"""
        return cls(
            _id=UUID(data["_id"]) if data.get("_id") else None,
            project_id=UUID(data["project_id"]),
            tenant_id=UUID(data["tenant_id"]),
            user_id=UUID(data["user_id"]),
            request_type=data["request_type"],
            status=data["status"],
            created_at=data["created_at"],
            updated_at=data["updated_at"],
            error_message = data["error_message"] if data.get("error_message") else None
        )

