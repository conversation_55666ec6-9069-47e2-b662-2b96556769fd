from typing import Optional
from uuid import UUID
from app.core.models.reaction import Reaction
from app.infra.db.entities.reaction_entity import ReactionEntity


class ReactionMapper:
    """Mapper for converting between Reaction domain model and ReactionEntity"""

    def to_domain_model(self, entity: ReactionEntity) -> Reaction:
        """Convert ReactionEntity to Reaction domain model"""
        if not entity:
            return None

        return Reaction(
            id=str(entity._id) if entity._id else None,
            tenant_id=str(entity.tenant_id),
            project_id=str(entity.project_id),
            route_id=str(entity.route_id),
            step_id=str(entity.step_id),
            identifiers=entity.identifiers,
            inputs=entity.inputs,
            setup=entity.setup,
            conditions=entity.conditions,
            created_by=str(entity.created_by) if entity.created_by else None,
            updated_by=str(entity.updated_by) if entity.updated_by else None,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
        )

    def to_entity_model(self, domain_model: Reaction) -> ReactionEntity:
        """Convert Reaction domain model to ReactionEntity"""
        if not domain_model:
            return None
        return ReactionEntity(
            _id=UUID(domain_model.id) if domain_model.id else None,
            tenant_id=UUID(domain_model.tenant_id),
            project_id=UUID(domain_model.project_id),
            route_id=UUID(domain_model.route_id),
            step_id=UUID(domain_model.step_id),
            identifiers=domain_model.identifiers,
            inputs=domain_model.inputs,
            setup=domain_model.setup,
            conditions=domain_model.conditions,
            created_by=UUID(domain_model.created_by) if domain_model.created_by else None,
            updated_by=UUID(domain_model.updated_by) if domain_model.updated_by else None,
            created_at=domain_model.created_at,
            updated_at=domain_model.updated_at,
        )
