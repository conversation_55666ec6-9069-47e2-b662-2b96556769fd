from datetime import datetime
from app.core.models.project_note import ProjectNote
from app.infra.db.entities.project_note_entity import ProjectNoteEntity


class ProjectNoteMapper:
    """Mapper for converting between ResearchWorkspace domain model and ResearchWorkspaceEntity"""

    def __init__(self):
        pass

    def to_domain_model(self, entity: ProjectNoteEntity) -> ProjectNote:

        if not entity:
            return None
        return ProjectNote(
            id=entity._id,
            tenant_id=entity.tenant_id,
            project_id=entity.project_id,
            note=entity.note,
            name=entity.name,
            description=entity.description,
            note_type=entity.note_type,
            note_type_id=entity.note_type_id,
            priority=entity.priority,
            created_at=entity.created_at,
            created_by=entity.created_by,
            updated_at=entity.updated_at,
            user_name=entity.user_name,
        )

    def to_entity_model(self, domain_model: ProjectNote) -> ProjectNoteEntity:
        """Convert ProjectNote domain model to ProjectNoteEntity """
        if not domain_model:
            return None
        return ProjectNoteEntity(
            _id=str(domain_model.id),
            tenant_id=str(domain_model.tenant_id),
            project_id=str(domain_model.project_id),
            note = domain_model.note,
            name = domain_model.name,
            description = domain_model.description,
            note_type = domain_model.note_type,
            note_type_id = domain_model.note_type_id,
            priority = domain_model.priority,
            created_by=str(domain_model.created_by),
            created_at=domain_model.created_at,
            updated_at=domain_model.updated_at,
            user_name=domain_model.user_name,
        )

