from typing import Optional
from uuid import UUID
from app.core.models.route import Route
from app.infra.db.entities.route_entity import RouteEntity


class RouteMapper:
    """Mapper for converting between Route domain model and RouteEntity"""

    def to_domain_model(self, entity: RouteEntity) -> Route:
        """Convert RouteEntity to Route domain model"""
        if not entity:
            return None

        return Route(
            id=str(entity._id) if entity._id else None,
            tenant_id=str(entity.tenant_id),
            project_id=str(entity.project_id),
            name=entity.name,
            description=entity.description,
            team=entity.team,
            status=entity.status,
            priority=entity.priority,
            steps_count=entity.steps_count,
            route_cost_in_usd=entity.route_cost_in_usd,
            certainty=entity.certainty,
            score=entity.score,
            rank=entity.rank,
            yield_value=entity.yield_value,
            approved_by=str(entity.approved_by) if entity.approved_by else None,
            approved_at=entity.approved_at,
            tags=entity.tags,
            attachments=entity.attachments,
            created_by=str(entity.created_by) if entity.created_by else None,
            updated_by=str(entity.updated_by) if entity.updated_by else None,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
        )

    def to_entity_model(self, domain_model: Route) -> RouteEntity:
        """Convert Route domain model to RouteEntity"""
        if not domain_model:
            return None

        return RouteEntity(
            _id=domain_model.id if domain_model.id else None,
            tenant_id=domain_model.tenant_id,
            project_id=domain_model.project_id,
            name=domain_model.name,
            description=domain_model.description,
            team=domain_model.team,
            status=domain_model.status,
            priority=domain_model.priority,
            steps_count=domain_model.steps_count,
            route_cost_in_usd=domain_model.route_cost_in_usd,
            certainty=domain_model.certainty,
            score=domain_model.score,
            rank=domain_model.rank,
            yield_value=domain_model.yield_value,
            approved_by=domain_model.approved_by,
            approved_at=domain_model.approved_at,
            tags=domain_model.tags,
            attachments=domain_model.attachments,
            created_by=domain_model.created_by if domain_model.created_by else None,
            updated_by=domain_model.updated_by if domain_model.updated_by else None,
            created_at=domain_model.created_at,
            updated_at=domain_model.updated_at,
        )
