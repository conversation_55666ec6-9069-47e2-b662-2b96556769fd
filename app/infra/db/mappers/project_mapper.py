from typing import Optional
from uuid import UUID, uuid4
from app.core.models.project import Project as CoreProject
from app.infra.db.entities.project_entity import ProjectEntity


class ProjectMapper:
    """Mapper for converting between Project domain model and ProjectEntity"""

    def __init__(self):
        pass

    def to_domain_model(self, entity: ProjectEntity) -> CoreProject:
        """Convert ProjectEntity to CoreProject"""
        if not entity:
            return None
        return CoreProject(
            id=entity._id,
            tenant_id=entity.tenant_id,
            description=entity.description,
            status=entity.status,
            compound_name=entity.compound_name,
            owner=entity.owner if entity.owner else None,
            canonical_smiles=entity.canonical_smiles,
            molecule_image_url=entity.molecule_image_url,
            shortlisted_routes_count=entity.shortlisted_routes_count,
            start_date=entity.start_date,
            end_date=entity.end_date,
            end_state=entity.end_state,
            approved_by=entity.approved_by if entity.approved_by else None,
            tags=entity.tags,
            collaborators= entity.collaborators,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
            created_by=entity.created_by if entity.created_by else None,
            updated_by=entity.updated_by if entity.updated_by else None,
        )

    def to_entity_model(self, domain_model: CoreProject) -> ProjectEntity:
        """Convert CoreProject to ProjectEntity"""
        if not domain_model:
            return None
        collaborators = (
            {user_id: collaborator.model_dump() for user_id, collaborator in domain_model.collaborators.items()}
            if domain_model.collaborators
            else None
        )
        return ProjectEntity(
            _id=str(domain_model.id),
            tenant_id=str(domain_model.tenant_id),
            description=domain_model.description,
            status=domain_model.status,
            compound_name=domain_model.compound_name,
            canonical_smiles=domain_model.canonical_smiles,
            molecule_image_url=domain_model.molecule_image_url,
            owner=str(domain_model.owner) if domain_model.owner else None,
            shortlisted_routes_count=domain_model.shortlisted_routes_count,
            start_date=domain_model.start_date,
            end_date=domain_model.end_date,
            end_state=domain_model.end_state,
            approved_by=str(domain_model.approved_by) if domain_model.approved_by else None,
            tags=domain_model.tags,
            collaborators=collaborators,
            created_at=domain_model.created_at,
            updated_at=domain_model.updated_at,
            created_by=str(domain_model.created_by) if domain_model.created_by else None,
            updated_by=str(domain_model.updated_by) if domain_model.updated_by else None,
        )
