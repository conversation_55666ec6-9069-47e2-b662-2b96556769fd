from app.infra.db.entities.retro_route_review_entity import RetroRouteReviewEntity
from app.core.models.retro_route_review import RetroRouteReview


class RetroRouteReviewMapper:
    """Mapper for converting between Route domain model and RouteEntity"""

    def __init__(self):
        pass

    def to_domain_model(self, entity: RetroRouteReviewEntity) -> RetroRouteReview:
        """Convert RetroRouteReviewEntity to a RetroRouteReview domain model"""
        if not entity:
            return None

        return RetroRouteReview(
            id=str(entity._id) if entity._id else None,
            tenant_id=str(entity.tenant_id),
            project_id=str(entity.project_id),
            retro_route_id=str(entity.retro_route_id),
            status=entity.status,
            review_text=entity.review_text,
            created_by=str(entity.created_by) if entity.created_by else None,
            updated_by=str(entity.updated_by) if entity.updated_by else None,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
        )

    def to_entity_model(self, domain_model: RetroRouteReview) -> RetroRouteReviewEntity:
        """Convert RetroRouteReview domain model to RetroRouteReviewEntity"""
        if not domain_model:
            return None

        return RetroRouteReviewEntity(
            _id=domain_model.id if domain_model.id else None,
            tenant_id=domain_model.tenant_id,
            project_id=domain_model.project_id,
            retro_route_id=domain_model.retro_route_id,
            status=domain_model.status,
            review_text=domain_model.review_text,
            created_by=domain_model.created_by if domain_model.created_by else None,
            updated_by=domain_model.updated_by if domain_model.updated_by else None,
            created_at=domain_model.created_at,
            updated_at=domain_model.updated_at,
        )
