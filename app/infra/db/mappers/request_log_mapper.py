from app.core.models.request_log import RequestLog
from app.infra.db.entities import RequestLogEntity

class RequestLogMapper:
    """Mapper between RequestLog domain model and RequestLogEntity"""

    def to_domain_model(self, entity: RequestLogEntity) -> RequestLog:
        if not entity:
            return None

        return RequestLog(
            id=entity._id,
            tenant_id=entity.tenant_id,
            project_id=entity.project_id,
            user_id=entity.user_id,
            request_type=entity.request_type,
            status=entity.status,
            created_at=entity.created_at,
            updated_at=entity.updated_at
        )


    def to_entity_model(self, domain_model: RequestLog) -> RequestLogEntity:
        if not domain_model:
            return None
        return RequestLogEntity(
            _id=str(domain_model.id),
            tenant_id=domain_model.tenant_id,
            project_id=domain_model.project_id,
            user_id=domain_model.user_id,
            request_type=domain_model.request_type,
            status=domain_model.status,
            created_at=domain_model.created_at,
            updated_at=domain_model.updated_at
        )

