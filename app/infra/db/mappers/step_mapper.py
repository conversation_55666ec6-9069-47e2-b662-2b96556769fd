from typing import Optional
from uuid import UUID

from app.core.models.step import Step
from app.infra.db.entities.step_entity import StepEntity


class StepMapper:
    """Mapper for converting between Step domain model and StepEntity"""

    def to_domain_model(self, entity: StepEntity) -> Step:
        """Convert StepEntity to Step domain model"""
        if not entity:
            return None

        return Step(
            id=str(entity._id) if entity._id else None,
            tenant_id=str(entity.tenant_id),
            project_id=str(entity.project_id),
            route_id=str(entity.route_id),
            name=entity.name,
            description=entity.description,
            step_number=entity.step_number,
            rxn_string=entity.rxn_string,
            rxn_type=entity.rxn_type,
            rxn_class=entity.rxn_class,
            rxn_superclass=entity.rxn_superclass,
            certainty=entity.certainty,
            predicted_yield=entity.predicted_yield,
            experimental_yield=entity.experimental_yield,
            notes=entity.notes,
            created_by=str(entity.created_by) if entity.created_by else None,
            updated_by=str(entity.updated_by) if entity.updated_by else None,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
        )

    def to_entity_model(self, domain_model: Step) -> StepEntity:
        """Convert Step domain model to StepEntity"""
        if not domain_model:
            return None

        return StepEntity(
            _id=domain_model.id if domain_model.id else None,
            tenant_id=domain_model.tenant_id,
            project_id=domain_model.project_id,
            route_id=domain_model.route_id,
            name=domain_model.name,
            description=domain_model.description,
            step_number=domain_model.step_number,
            rxn_string=domain_model.rxn_string,
            rxn_type=domain_model.rxn_type,
            rxn_class=domain_model.rxn_class,
            rxn_superclass=domain_model.rxn_superclass,
            certainty=domain_model.certainty,
            predicted_yield=domain_model.predicted_yield,
            experimental_yield=domain_model.experimental_yield,
            notes=domain_model.notes,
            created_by=domain_model.created_by if domain_model.created_by else None,
            updated_by=domain_model.updated_by if domain_model.updated_by else None,
            created_at=domain_model.created_at,
            updated_at=domain_model.updated_at,
        )
