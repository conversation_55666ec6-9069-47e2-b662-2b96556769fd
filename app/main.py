import logging

from fastapi import FastAPI, HTTPException
from loguru import logger

# Import all route modules
from app.api.routes import (
    project_router,
    route_router,
    step_router,
    reaction_router,
    patent_router,
    project_notes_router,
    retro_route_router,
    research_overview_router
)
from app.common.global_exception_handler import http_exception_handler, global_exception_handler
from app.config.app_config import app_config
from app.config.logging_config import setup_logging
from app.infra.mongo_client.chemstack_db_client import chemstack_db_client
from app.infra.mongo_client.retro_db_client import retro_db_client
from app.middleware import logging_middleware, auth_middleware
from app.monitoring.health import health_router
from app.monitoring.metrics import metrics_middleware
from app.monitoring.metrics import metrics_router

setup_logging(log_level=app_config.LOGGING_LEVEL)
logging.getLogger("azure").setLevel(logging.WARNING)


# Combine a root path with doc URL
root_path = f"/{app_config.BASE_PATH}"
docs_url = "/docs"
redoc_url = "/redoc"

app = FastAPI(
    title=app_config.PROJECT_NAME,
    description="API Documentation",
    root_path=root_path,
    docs_url=docs_url,
    redoc_url=redoc_url,
    version="1.0.0",
)

# Register exception handlers
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(Exception, global_exception_handler)

# Add middlewares
app.middleware("http")(logging_middleware)
app.middleware("http")(auth_middleware)

# Include chemstack routers
app.include_router(project_router, prefix="/api/v1", tags=["Project Management"])
app.include_router(retro_route_router, prefix="/api/v1", tags=["AI: Retro Route Management"])
app.include_router(patent_router, prefix="/api/v1", tags=["AI: Literature Content Management"])
app.include_router(project_notes_router, prefix="/api/v1", tags=["Project Notes"])
# Experimentation
app.include_router(route_router, prefix="/api/v1", tags=["Experimentation: Route Management"])
app.include_router(step_router, prefix="/api/v1", tags=["Experimentation: Step Management"])
app.include_router(reaction_router, prefix="/api/v1", tags=["Experimentation: Reaction Management"])
app.include_router(research_overview_router, prefix="/api/v1", tags=["Research Overview"])


# Metrics middleware
app.middleware("http")(metrics_middleware)

# Include monitoring routers
app.include_router(metrics_router, tags=["Monitoring"])
app.include_router(health_router)


@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    try:
        # Connect to MongoDB
        await chemstack_db_client.connect()
        await retro_db_client.connect()
        logger.info("Connected to MongoDB and Retro DB")
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB or Retro DB: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup services on shutdown"""
    try:
        # Disconnect from MongoDB
        await chemstack_db_client.disconnect()
        await retro_db_client.disconnect()
        logger.info("Disconnected from MongoDB and Retro DB")
    except Exception as e:
        logger.error(f"Error disconnecting from MongoDB or Retro DB: {e}")
