#!/bin/bash

# Start the server
echo "Starting server..."

# Start Celery worker in background
celery -A app.worker worker --loglevel=info 2>&1 | sed 's/^/[celery] /' &
CELERY_PID=$!

# Start FastAPI server in background
gunicorn app.main:app -k uvicorn.workers.UvicornWorker --timeout 480 -b 0.0.0.0:8000 2>&1 | sed 's/^/[api] /' &
API_PID=$!

echo "Server started"
echo "Celery PID: $CELERY_PID"
echo "API PID: $API_PID"

# Wait for either to exit
wait -n
exit_code=$?

# Kill the other process to avoid orphaning
kill $CELERY_PID $API_PID 2>/dev/null

# Exit with the status of the one that exited first
exit $exit_code
