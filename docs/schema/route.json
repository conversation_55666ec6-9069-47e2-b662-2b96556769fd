{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://example.com/schemas/route.schema.json", "title": "Synthesis Route", "description": "Schema for a synthesis route under a project", "type": "object", "required": ["id", "project_id", "name", "created_by", "created_at"], "properties": {"id": {"type": "string", "pattern": "^route#[a-zA-Z0-9\\-]+$"}, "project_id": {"type": "string", "pattern": "^proj#[a-zA-Z0-9\\-]+$"}, "name": {"type": "string"}, "description": {"type": "string"}, "team": {"type": "string"}, "status": {"type": "string", "enum": ["planned", "in_progress", "completed", "abandoned"]}, "priority": {"type": "string", "enum": ["low", "medium", "high"]}, "steps_count": {"type": "integer", "minimum": 0}, "route_cost": {"type": "number", "description": "Estimated cost of the synthesis route in chosen currency (e.g., USD)"}, "certainty": {"type": "number", "minimum": 0, "maximum": 1, "description": "Confidence score (0 to 1) from model or user estimate"}, "score": {"type": "number", "description": "Composite score used for route ranking"}, "rank": {"type": "integer", "minimum": 1, "description": "Relative rank among all available routes for this project"}, "yield": {"type": "number", "minimum": 0, "maximum": 100, "description": "Estimated or observed yield in percentage (%)"}}, "created_by": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "approved_by": {"type": ["string", "null"]}, "approved_at": {"type": ["string", "null"], "format": "date-time"}, "tags": {"type": "array", "items": {"type": "string"}}, "attachments": {"type": "array", "items": {"type": "object", "required": ["filename", "url"], "properties": {"filename": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "uploaded_by": {"type": "string"}, "uploaded_at": {"type": "string", "format": "date-time"}}}}, "additionalProperties": false}