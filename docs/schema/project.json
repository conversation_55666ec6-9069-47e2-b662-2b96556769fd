{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://example.com/schemas/project.schema.json", "title": "Chemical Synthesis Project", "description": "Schema for a synthesis project entity", "type": "object", "required": ["id", "name", "owner", "compound_name", "canonical_smiles", "start_date", "status"], "properties": {"id": {"type": "string", "description": "Unique identifier for the project: UUID"}, "name": {"type": "string", "description": "Human-readable project name"}, "owner": {"type": "string", "description": "User ID or name of the project owner"}, "compound_name": {"type": "string", "description": "Name of the compound to be synthesized"}, "canonical_smiles": {"type": "string", "description": "Canonical SMILES notation of the compound"}, "start_date": {"type": "string", "format": "date-time", "description": "Start date of the project"}, "end_date": {"type": ["string", "null"], "format": "date-time", "description": "End date of the project (nullable if ongoing)"}, "status": {"type": "string", "enum": ["draft", "in_progress", "completed", "on_hold", "cancelled"], "description": "Current status of the project"}, "end_state": {"type": ["string", "null"], "description": "Final outcome or notes about project conclusion"}, "approved_by": {"type": ["string", "null"], "description": "User ID of approver (nullable if not yet approved)"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp when the project was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "Timestamp of last update"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Optional tags for project classification"}, "description": {"type": "string", "description": "Optional long description or objective"}}, "additionalProperties": false}