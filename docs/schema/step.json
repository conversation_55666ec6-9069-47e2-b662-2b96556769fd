{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://example.com/schemas/step.schema.json", "title": "Synthesis Step", "description": "Schema representing a single step in a synthesis route", "type": "object", "required": ["id", "route_id", "step_number", "rxn_string", "rxn_type", "created_at"], "properties": {"id": {"type": "string", "description": "Unique identifier for the step"}, "route_id": {"type": "string", "description": "Reference to the parent synthesis route"}, "step_number": {"type": "integer", "minimum": 1, "description": "The order of the step within the route"}, "rxn_string": {"type": "string", "description": "Reaction in RXN format (e.g., SMILES or RXN block)"}, "rxn_type": {"type": "string", "description": "Descriptive reaction type (e.g., 'Chloride salt formation')"}, "rxn_class": {"type": "string", "description": "Reaction classification (e.g., 'Salt formation')"}, "rxn_superclass": {"type": "string", "description": "Reaction superclass (e.g., 'Separation and resolution')"}, "certainty": {"type": "number", "minimum": 0, "maximum": 1, "description": "Confidence score (e.g., from model prediction)"}, "predicted_yield": {"type": "number", "minimum": 0, "maximum": 100, "description": "Predicted yield percentage for the step"}, "experimental_yield": {"type": ["number", "null"], "minimum": 0, "maximum": 100, "description": "Actual observed yield percentage (if available)"}, "notes": {"type": "string", "description": "Optional notes or explanation from chemist"}, "created_by": {"type": "string", "description": "User ID of the person who added the step"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp of creation"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "additionalProperties": false}