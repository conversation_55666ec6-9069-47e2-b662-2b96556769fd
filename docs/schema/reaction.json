{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Reaction Schema", "description": "JSON Schema for Reaction (ORD) entries", "type": "object", "required": ["id"], "properties": {"id": {"type": "string", "description": "Unique identifier for the reaction"}, "step_id": {"type": "string", "description": "Reference to the parent step"}, "identifiers": {"type": "array", "description": "Identifiers for the reaction", "items": {"type": "object", "required": ["type", "value"], "properties": {"type": {"type": "string", "description": "Type of the identifier"}, "details": {"type": "string", "description": "Additional details about the identifier"}, "value": {"type": "string", "description": "Value of the identifier"}}}}, "inputs": {"type": "array", "description": "Input components used in the reaction", "items": {"type": "object", "required": ["input_name"], "properties": {"input_name": {"type": "string", "description": "Name of the input"}, "components": {"type": "array", "items": {"type": "object", "properties": {"identifiers": {"type": "array", "items": {"type": "object", "required": ["type", "value"], "properties": {"type": {"type": "string", "description": "Identifier type (e.g., SMILES, NAME)"}, "value": {"type": "string", "description": "Identifier value"}, "details": {"type": "string", "description": "Additional details about the identifier"}}}}, "amount": {"type": "object", "description": "Amount of the component", "properties": {"moles": {"type": "object", "properties": {"value": {"type": "number", "description": "Numerical value"}, "units": {"type": "string", "description": "Units (e.g., MILLIMOLE, MICROMOLE)"}}}, "volume": {"type": "object", "properties": {"value": {"type": "number", "description": "Numerical value"}, "units": {"type": "string", "description": "Units (e.g., MICROLITER)"}}}, "volume_includes_solutes": {"type": "boolean", "description": "Whether the volume includes the solutes"}}}, "reaction_role": {"type": "string", "description": "Role in the reaction (e.g., REACTANT, REAGENT, SOLVENT, CATALYST, INTERNAL_STANDARD)"}, "is_limiting": {"type": "boolean", "description": "Whether this component is limiting"}, "preparations": {"type": "array", "description": "Preparations applied to the component", "items": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of preparation (e.g., DRIED)"}}}}}}}, "addition_order": {"type": "integer", "description": "Order in which this input is added"}, "addition_speed": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of addition speed (e.g., ALL_AT_ONCE)"}, "details": {"type": "string", "description": "Additional details about the addition"}}}}}}, "setup": {"type": "object", "description": "Reaction setup details", "properties": {"vessel": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of vessel (e.g., VIAL)"}, "material": {"type": "object", "properties": {"type": {"type": "string", "description": "Material of the vessel (e.g., GLASS)"}}}, "attachments": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of attachment (e.g., CAP)"}}}}}}, "environment": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of environment (e.g., GLOVE_BOX)"}}}}}, "conditions": {"type": "object", "description": "Reaction conditions", "properties": {"temperature": {"type": "object", "properties": {"setpoint": {"type": "object", "properties": {"value": {"type": "number", "description": "Temperature value"}, "units": {"type": "string", "description": "Temperature units (e.g., CELSIUS)"}}}}}, "pressure": {"type": "object", "properties": {"control": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of pressure control (e.g., SEALED)"}}}}}, "stirring": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of stirring (e.g., AGITATION)"}}}}}, "workups": {"type": "array", "description": "Workup procedures", "items": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of workup (e.g., ADDITION, STIRRING, ALIQUOT, FILTRATION)"}, "details": {"type": "string", "description": "Details about the workup"}, "input": {"type": "object", "properties": {"components": {"type": "array", "items": {"type": "object", "properties": {"identifiers": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "value": {"type": "string"}, "details": {"type": "string"}}}}, "amount": {"type": "object", "properties": {"volume": {"type": "object", "properties": {"value": {"type": "number"}, "units": {"type": "string"}}}}}, "reaction_role": {"type": "string"}}}}}}, "duration": {"type": "object", "properties": {"value": {"type": "number"}, "units": {"type": "string"}}}, "amount": {"type": "object", "properties": {"volume": {"type": "object", "properties": {"value": {"type": "number"}, "units": {"type": "string"}}}}}, "keep_phase": {"type": "string", "description": "Phase to keep (e.g., organic)"}}}}, "outcomes": {"type": "array", "description": "Results of the reaction", "items": {"type": "object", "properties": {"reaction_time": {"type": "object", "properties": {"value": {"type": "number", "description": "Reaction time value"}, "units": {"type": "string", "description": "Time units (e.g., HOUR)"}}}, "products": {"type": "array", "items": {"type": "object", "properties": {"identifiers": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "value": {"type": "string"}, "details": {"type": "string"}}}}, "is_desired_product": {"type": "boolean", "description": "Whether this is the desired product"}, "measurements": {"type": "array", "items": {"type": "object", "properties": {"analysis_key": {"type": "string"}, "type": {"type": "string", "description": "Type of measurement (e.g., YIELD)"}, "details": {"type": "string"}, "uses_internal_standard": {"type": "boolean"}, "is_normalized": {"type": "boolean"}, "percentage": {"type": "object", "properties": {"value": {"type": "number", "description": "Percentage value"}}}}}}, "reaction_role": {"type": "string", "description": "Role in the reaction (e.g., PRODUCT, INTERNAL_STANDARD)"}}}}, "analyses": {"type": "object", "additionalProperties": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of analysis (e.g., LC)"}, "details": {"type": "string", "description": "Details about the analysis"}, "is_of_isolated_species": {"type": "boolean", "description": "Whether the analysis is of isolated species"}}}}}}}, "provenance": {"type": "object", "description": "Information about the origin of the data", "properties": {"experimenter": {"type": "object", "properties": {"organization": {"type": "string", "description": "Organization conducting the experiment"}}}, "city": {"type": "string", "description": "City where the experiment was conducted"}, "record_created": {"type": "object", "properties": {"time": {"type": "object", "properties": {"value": {"type": "string", "description": "Timestamp of record creation"}}}, "person": {"type": "object", "properties": {"name": {"type": "string"}, "organization": {"type": "string"}, "email": {"type": "string", "format": "email"}}}}}, "record_modified": {"type": "array", "items": {"type": "object", "properties": {"time": {"type": "object", "properties": {"value": {"type": "string", "description": "Timestamp of record modification"}}}, "person": {"type": "object", "properties": {"username": {"type": "string"}, "email": {"type": "string", "format": "email"}}}, "details": {"type": "string", "description": "Details about the modification"}}}}}}}}