# chemstack-api - Backend for the chemstack

## Overview


## Prerequisites
- Python 3.11+
- Git

## Development Setup
```sh
# Clone the repository
<NAME_EMAIL>:Mstack-Chemicals/chemstack-api.git
cd chemstack-api

# Create and activate virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# For development dependencies
pip install -r requirements-dev.txt

# Run the application locally with hot reload
uvicorn app.main:app --reload
```

## Environment Configuration
Create a `.env` file in the root directory with the following configuration:

```env
ENV=local
# Vault is not mandatory
VAULT_ADDR=<VAULT_URL>
VAULT_TOKEN=<VAULT_TOKEN>
```

## Project Structure
![Heaxagon Pattern](./docs/hexagonal.png)

The project follows the hexagonal pattern 
```
app/
├── api/                # API layer
│   ├── decorators/        # Auth decorators for authorization
│   ├── routes/            # API endpoints
│   ├── schemas/           # Pydantic models
│   └── dependencies.py    # Dependency Management
├── core/               # Core business logic
│   ├── models/            # Domain models
│   ├── services/          # Business services
│   └── outbound/          # Repository interfaces
├── infra/              # Infrastructure layer
│   ├── db/                # Database implementations
│   │   ├── repositories/     # Repository implementations
│   │   ├── models/
│   └── social/            # Database implementations
├── middleware/         # Custom middleware
├── common/             # Shared utilities
├── config/             # Configuration management
└── main.py             # App Entry Point
```



## Features

### 1. Authentication & Authorization
- JWT-based authentication
- Role-based access control (RBAC)
- Privilege-based access control (PBAC)
- Token refresh mechanism
- Social authentication support


## API Documentation

### Swagger UI
```
http://localhost:8000/chemstack/docs
```

### ReDoc
```
http://localhost:8000/chemstack/redoc
```

## Monitoring

### Health Checks
```
/chemstack/health
/chemstack/health/live
/chemstack/health/ready
```

### Metrics
```
/chemstack/metrics
```

## Database Migrations
```sh
# TODO
```

## Testing
```bash
# Run all tests
pytest

# Run tests with coverage
pytest --cov=app tests/

# Run specific test file
pytest tests/core/test_user_service.py

# Generate HTML coverage report
pytest --cov=app --cov-report=html tests/
```


## Git Workflow

This repository follows a **trunk-based development** approach. All changes are branched from and merged back into `main`.


### 1. Feature Development

```bash
git checkout main
git pull
git checkout -b feature/feature-name
# Make changes
git add .
git commit -m "feat: add feature description"
git push origin feature/feature-name
```

- Open a Pull Request (PR) from `feature/feature-name` into `main`
- After review and approval, merge the PR
- Optionally, clean up the branch locally:

```bash
git checkout main
git pull
git branch -d feature/feature-name
```

### 2. Hotfix

```bash
git checkout main
git pull
git checkout -b hotfix/fix-description
# Make fixes
git add .
git commit -m "fix: short description of fix"
git push origin hotfix/fix-description
```

- Open a PR from `hotfix/fix-description` into `main`
- After merge, create a patch release tag:

```bash
git checkout main
git pull
git tag -a v1.0.1 -m "Hotfix: fix description"
git push origin v1.0.1
```

### 3. Release

```bash
git checkout main
git pull
# Optionally generate a changelog here
git tag -a v1.0.0 -m "Release version 1.0.0 - includes features A, B, and C"
git push origin v1.0.0
```

## Deployment
```bash
# Build the Docker image
docker build -t chemstack-api:latest .

# Run the container
docker run -d \
    --name chemstack-api \
    -p 8000:8000 \
    -e ENV=prod \
    chemstack-api:latest
```


## TODO
1. Caching
2. ...

## Contributing
1. Fork the repository
2. Create your feature branch from `main`
3. Commit your changes with descriptive commit messages
4. Push to your branch
5. Create a Pull Request

## License
[Specify License]

## Contact
[Contact Information]
