# Stage 1: Build stage
FROM python:3.13-slim as builder

# Set working directory
WORKDIR /app

# Install system dependencies required for building Python packages
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libxrender1 \
    libxext6 \
    libsm6 \
    libglib2.0-0 \
    libfreetype6 \
    libpng16-16 \
    libfontconfig1 \
    libexpat1 \
    fonts-dejavu-core \
    unzip \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .

RUN pip wheel --no-cache-dir --wheel-dir /app/wheels -r requirements.txt

# Stage 2: Production stage
FROM python:3.13-slim

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    libxrender1 \
    libxext6 \
    libsm6 \
    libglib2.0-0 \
    libfreetype6 \
    libpng16-16 \
    libfontconfig1 \
    libexpat1 \
    fonts-dejavu-core \
    unzip \
    libpq5 \
    && rm -rf /var/lib/apt/lists/*

# Copy wheels from builder stage
COPY --from=builder /app/wheels /wheels
COPY --from=builder /app/requirements.txt .

# Install Python packages
RUN pip install --no-cache /wheels/* && \
    pip install -r requirements.txt

# Copy application code
COPY app app/
COPY default.env /app/
COPY start-server.sh /app/

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PORT=8000

# Switch to non-root user
USER appuser

# Expose port
EXPOSE ${PORT}

# Start the application with Gunicorn
CMD ["bash", "start-server.sh"]