ENV=local
PROJECT_NAME=chemstack-api
BASE_PATH=chemstack
LOGGING_LEVEL=INFO
TZ=UTC

# MongoDB Connection Settings
COMMON_MONGODB_MAX_POOL_SIZE=100
COMMON_MONGODB_MIN_POOL_SIZE=10
COMMON_MONGODB_MAX_IDLE_TIME_MS=120000
COMMON_MONGODB_CONNECT_TIMEOUT_MS=20000
COMMON_MONGODB_SERVER_SELECTION_TIMEOUT_MS=5000
COMMON_MONGODB_AUTH_SOURCE=admin
COMMON_MONGODB_TLS=true
COMMON_MONGODB_AUTH_MECHANISM=SCRAM-SHA-256
COMMON_MONGODB_RETRY_WRITES=false

CHEMSTACK_MONGODB_CONNECTION_STRING = mongodb+srv://chemstack:<EMAIL>/synthesissuite?tls=true&authMechanism=SCRAM-SHA-256&retrywrites=false&maxIdleTimeMS=120000
CHEMSTACK_MONGODB_DB_NAME= chemstack_db
RETRO_MONGODB_CONNECTION_STRING = mongodb+srv://chemstack:<EMAIL>/synthesissuite?tls=true&authMechanism=SCRAM-SHA-256&retrywrites=false&maxIdleTimeMS=120000
RETRO_MONGODB_DB_NAME = retro_synthesis

# Azure Search Settings (dummy values)
AZURE_SEARCH_ENDPOINT=https://dummy-search.search.windows.net
AZURE_SEARCH_API_KEY=dummy-api-key-1234567890abcdef
AZURE_SEARCH_INDEX=dummy-index
AZURE_SEARCH_API_VERSION=2023-11-01

# OpenAI Settings (dummy values)
OPENAI_API_KEY=sk-dummy-openai-key-1234567890abcdef

# Redis Settings (dummy values)
REDIS_URL=redis://localhost:6379
RETRO_REQUEST_REDIS_QUEUE=retro_request_queue
RETRO_RESPONSE_REDIS_QUEUE=retro_response_queue
PATENT_REQUEST_REDIS_QUEUE=patent_request_queue
PATENT_RESPONSE_REDIS_QUEUE=patent_response_queue

# Azure Blob Storage Settings (dummy values)
AZURE_BLOB_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=dummyaccount;AccountKey=dummykey;EndpointSuffix=core.windows.net
AZURE_BLOB_CONTAINER_NAME=dummy-container

# CDN Settings (dummy values)
CDN_URL=https://dummy-cdn.azureedge.net