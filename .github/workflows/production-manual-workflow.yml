name: production-manual-workflow  # Manual Deployment Workflow

on:
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch to deploy"
        required: true
        default: "main"

permissions:
  id-token: write
  contents: read

jobs:
  build_and_push:
    runs-on: ubuntu-latest
    outputs:
      image_tag: ${{ steps.export_image_tag.outputs.image_tag }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        ref: ${{ github.event.inputs.branch }}
        lfs: true

    - name: Extract commit SHA and set IMAGE_TAG
      id: export_image_tag
      run: |
        IMAGE_TAG=$(git rev-parse --short HEAD)
        echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV
        echo "image_tag=$IMAGE_TAG" >> $GITHUB_OUTPUT

    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}

    - name: Check if image exists in ACR
      id: check_image
      run: |
        exists=$(az acr repository show-manifests \
          --name ${{ vars.AZURE_CONTAINER_REGISTRY_NAME }} \
          --repository chemstack-api \
          --query "[?tags[?contains(@, '${{ env.IMAGE_TAG }}')]] | length(@)" \
          -o tsv 2>/dev/null || echo 0)

        echo "Image exists: $exists"
        echo "exists=$([ "$exists" -gt 0 ] && echo true || echo false)" >> $GITHUB_OUTPUT

    - name: Docker Login to ACR
      if: steps.check_image.outputs.exists == 'false'
      run: |
        docker login -u ${{ secrets.ACR_USERNAME }} -p ${{ secrets.ACR_PASSWORD }} ${{ vars.AZURE_CONTAINER_REGISTRY_URL }}

    - name: Build and Push Docker Image
      if: steps.check_image.outputs.exists == 'false'
      run: |
        docker build -t ${{ vars.AZURE_CONTAINER_REGISTRY_URL }}/chemstack-api:${{ env.IMAGE_TAG }} .
        docker push ${{ vars.AZURE_CONTAINER_REGISTRY_URL }}/chemstack-api:${{ env.IMAGE_TAG }}

    - name: Logout from Docker
      if: steps.check_image.outputs.exists == 'false'
      run: docker logout
  trigger_argocd_deployment:
    needs: build_and_push
    runs-on: ubuntu-latest
    if: needs.build_and_push.outputs.image_tag != ''

    steps:
    - name: Checkout app-deployment repo
      uses: actions/checkout@v3
      with:
        repository: Mstack-Chemicals/app-deployment
        token: ${{ secrets.DEPLOYMENT_PERSONAL_AT }}
        ref: main

    - name: Update image tag
      run: |
        FILE=chemstack/chemstack-api/production.values.yaml
        OLD_TAG=$(grep 'tag:' "$FILE" | awk '{ print $2 }')
        sed -i "s/tag: $OLD_TAG/tag: \"${{ needs.build_and_push.outputs.image_tag }}\"/" "$FILE"
        echo "Updated tag in $FILE from $OLD_TAG to ${{ needs.build_and_push.outputs.image_tag }}"

    - name: Commit and push changes
      run: |
        git config user.name "GitHub Actions"
        git config user.email "<EMAIL>"
        git add .
        git commit -m "chore: update chemstack-api tag to ${{ needs.build_and_push.outputs.image_tag }}" || echo "No changes to commit"
        git push origin main
        